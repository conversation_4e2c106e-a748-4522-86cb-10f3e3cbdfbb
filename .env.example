APP_NAME=OLLIE
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=pgsql
DB_HOST=pgsql
DB_PORT=5432
DB_DATABASE=laravel
DB_USERNAME=
DB_PASSWORD=

SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=public

QUEUE_CONNECTION=redis
CACHE_STORE=redis
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=valkey
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS2_ACCESS_KEY_ID=
AWS2_SECRET_ACCESS_KEY=


AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

OPENAI_API_KEY=
OPENAI_ORGANIZATION=

GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

WILLBANK_BUCKET=ol-will-bank
OLLIE_BUCKET=ollie

TELESCOPE_ENABLED=false

OCR_DRIVER=aws
OCR_API_KEY=

NGROK_AUTH_TOKEN=
HUBSPOT_ACCESS_TOKEN=
HUBSPOT_PORTAL_ID=

CLOUDWATCH_LOG_NAME=
CLOUDWATCH_LOG_REGION=
CLOUDWATCH_LOG_KEY=
CLOUDWATCH_LOG_SECRET=
CLOUDWATCH_LOG_STREAM_NAME=ollie-app-${APP_ENV}
CLOUDWATCH_LOG_RETENTION_DAYS=14
CLOUDWATCH_LOG_GROUP_NAME=ollie-app
CLOUDWATCH_LOG_VERSION=latest
CLOUDWATCH_LOG_BATCH_SIZE=10000
