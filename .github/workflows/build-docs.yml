name: Build Documentation

on:
  push:
    branches: [ main, master ]
    paths:
      - 'docs/**'
      - '.github/workflows/build-docs.yml'
  pull_request:
    branches: [ main, master ]
    paths:
      - 'docs/**'
      - '.github/workflows/build-docs.yml'
  workflow_dispatch: # Allow manual triggering

jobs:
  build-docs:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build documentation
      run: npm run docs:build
      
    - name: Check if docs were built
      run: |
        if [ ! -d "public/docs" ]; then
          echo "❌ Documentation build failed - public/docs directory not found"
          exit 1
        fi
        
        if [ ! -f "public/docs/index.html" ]; then
          echo "❌ Documentation build failed - index.html not found"
          exit 1
        fi
        
        echo "✅ Documentation built successfully"
        echo "📁 Files in public/docs:"
        ls -la public/docs/
        
    - name: Upload documentation artifacts
      uses: actions/upload-artifact@v4
      with:
        name: documentation
        path: public/docs/
        retention-days: 30
        
    - name: Comment on PR (if applicable)
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const { owner, repo, number } = context.issue;
          
          const comment = `## 📖 Documentation Build Status
          
          ✅ Documentation has been built successfully!
          
          The documentation will be available at \`/docs\` once this PR is merged.
          
          **Build Details:**
          - **Commit:** ${context.sha.substring(0, 7)}
          - **Workflow:** [${context.workflow}](${context.payload.repository.html_url}/actions/runs/${context.runId})
          
          You can download the built documentation from the [workflow artifacts](${context.payload.repository.html_url}/actions/runs/${context.runId}).`;
          
          github.rest.issues.createComment({
            owner,
            repo,
            issue_number: number,
            body: comment
          });


