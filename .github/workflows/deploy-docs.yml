name: Deploy Documentation

on:
  push:
    branches: [ main, master ]
    paths:
      - 'docs/**'
      - 'package.json'
      - 'package-lock.json'
      - '.github/workflows/deploy-docs.yml'
  workflow_dispatch: # Allow manual triggering

jobs:
  deploy-docs:
    runs-on: ubuntu-latest
    
    # Only run on main/master branch pushes, not PRs
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build documentation
      run: npm run docs:build
      
    - name: Check if docs were built
      run: |
        if [ ! -d "public/docs" ]; then
          echo "❌ Documentation build failed - public/docs directory not found"
          exit 1
        fi
        
        if [ ! -f "public/docs/index.html" ]; then
          echo "❌ Documentation build failed - index.html not found"
          exit 1
        fi
        
        echo "✅ Documentation built successfully"
        echo "📊 Documentation stats:"
        echo "Files: $(find public/docs -type f | wc -l)"
        echo "Size: $(du -sh public/docs | cut -f1)"
        
    - name: Configure Git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        
    - name: Commit and push built documentation
      run: |
        # Add the built documentation
        git add public/docs/
        
        # Check if there are changes to commit
        if git diff --staged --quiet; then
          echo "📝 No changes to documentation detected"
        else
          echo "📝 Committing updated documentation"
          git commit -m "docs: update built documentation [skip ci]
          
          - Built from commit: ${{ github.sha }}
          - Workflow: ${{ github.workflow }}
          - Run: ${{ github.run_number }}"
          
          git push
          
          echo "✅ Documentation deployed successfully"
        fi
        
    - name: Create deployment summary
      run: |
        echo "## 📖 Documentation Deployment Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "✅ **Status:** Successfully deployed" >> $GITHUB_STEP_SUMMARY
        echo "🔗 **URL:** \`/docs\` (authentication required)" >> $GITHUB_STEP_SUMMARY
        echo "📊 **Files:** $(find public/docs -type f | wc -l)" >> $GITHUB_STEP_SUMMARY
        echo "💾 **Size:** $(du -sh public/docs | cut -f1)" >> $GITHUB_STEP_SUMMARY
        echo "🕐 **Built at:** $(date -u '+%Y-%m-%d %H:%M:%S UTC')" >> $GITHUB_STEP_SUMMARY
        echo "📝 **Commit:** [\`${GITHUB_SHA:0:7}\`](${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/commit/${GITHUB_SHA})" >> $GITHUB_STEP_SUMMARY
