name: Rebuild Documentation

on:
  workflow_dispatch:
    inputs:
      force_rebuild:
        description: 'Force rebuild even if no changes detected'
        required: false
        default: false
        type: boolean
      commit_message:
        description: 'Custom commit message (optional)'
        required: false
        default: ''
        type: string

jobs:
  rebuild-docs:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        fetch-depth: 0 # Fetch full history for better commit messages
        
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Clean existing documentation
      run: |
        if [ -d "public/docs" ]; then
          echo "🧹 Cleaning existing documentation"
          rm -rf public/docs
        fi
        
    - name: Build documentation
      run: npm run docs:build
      
    - name: Validate documentation build
      run: |
        if [ ! -d "public/docs" ]; then
          echo "❌ Documentation build failed - public/docs directory not found"
          exit 1
        fi
        
        if [ ! -f "public/docs/index.html" ]; then
          echo "❌ Documentation build failed - index.html not found"
          exit 1
        fi
        
        echo "✅ Documentation built successfully"
        echo "📊 Documentation stats:"
        echo "Files: $(find public/docs -type f | wc -l)"
        echo "Size: $(du -sh public/docs | cut -f1)"
        echo "📁 Structure:"
        find public/docs -type d | head -10
        
    - name: Configure Git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action (Manual Rebuild)"
        
    - name: Commit and push rebuilt documentation
      run: |
        # Add the built documentation
        git add public/docs/
        
        # Check if there are changes to commit or if force rebuild is requested
        if git diff --staged --quiet && [ "${{ github.event.inputs.force_rebuild }}" != "true" ]; then
          echo "📝 No changes to documentation detected and force rebuild not requested"
          echo "skip_commit=true" >> $GITHUB_ENV
        else
          # Determine commit message
          if [ -n "${{ github.event.inputs.commit_message }}" ]; then
            COMMIT_MSG="${{ github.event.inputs.commit_message }}"
          else
            COMMIT_MSG="docs: rebuild documentation [skip ci]"
          fi
          
          echo "📝 Committing rebuilt documentation"
          git commit -m "${COMMIT_MSG}
          
          - Manually triggered rebuild
          - Force rebuild: ${{ github.event.inputs.force_rebuild }}
          - Workflow: ${{ github.workflow }}
          - Run: ${{ github.run_number }}
          - Triggered by: ${{ github.actor }}"
          
          git push
          
          echo "✅ Documentation rebuilt and deployed successfully"
          echo "skip_commit=false" >> $GITHUB_ENV
        fi
        
    - name: Create rebuild summary
      run: |
        echo "## 🔄 Documentation Rebuild Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        if [ "${{ env.skip_commit }}" = "true" ]; then
          echo "ℹ️ **Status:** No changes detected, no commit made" >> $GITHUB_STEP_SUMMARY
        else
          echo "✅ **Status:** Successfully rebuilt and deployed" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "🔗 **URL:** \`/docs\` (authentication required)" >> $GITHUB_STEP_SUMMARY
        echo "📊 **Files:** $(find public/docs -type f | wc -l)" >> $GITHUB_STEP_SUMMARY
        echo "💾 **Size:** $(du -sh public/docs | cut -f1)" >> $GITHUB_STEP_SUMMARY
        echo "🕐 **Built at:** $(date -u '+%Y-%m-%d %H:%M:%S UTC')" >> $GITHUB_STEP_SUMMARY
        echo "👤 **Triggered by:** ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY
        echo "⚙️ **Force rebuild:** ${{ github.event.inputs.force_rebuild }}" >> $GITHUB_STEP_SUMMARY
        
        if [ -n "${{ github.event.inputs.commit_message }}" ]; then
          echo "💬 **Custom message:** ${{ github.event.inputs.commit_message }}" >> $GITHUB_STEP_SUMMARY
        fi
