# O.L.L.I.E
## Octopus Legacy Intelligence Engine

### Introduction
This application is a toolbox for AI agents and workflows to aid Octopus Legacy's operations. 

### Installation
1. Clone the repository
2. Run `composer install`
3. Run `sail up`
4. Run `sail artisan migrate`
5. Run `sail artisan db:seed`
6. Run `sail npm run dev`
7. Visit `http://localhost` in your browser
8. Profit.

### Radar - process documents
1. Run `php artisan queue:work`
2. Run `php artisan horizon`
3. Upload documents should now process

### Server Requirements
- PHP >= 8.3
- ImageMagick
- TessaractOCR
