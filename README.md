# O.L.L.I.E
## Octopus Legacy Intelligence Engine

[![Build Documentation](https://github.com/your-org/ollie/actions/workflows/build-docs.yml/badge.svg)](https://github.com/your-org/ollie/actions/workflows/build-docs.yml)
[![Deploy Documentation](https://github.com/your-org/ollie/actions/workflows/deploy-docs.yml/badge.svg)](https://github.com/your-org/ollie/actions/workflows/deploy-docs.yml)

### Introduction
This application is a toolbox for AI agents and workflows to aid Octopus Legacy's operations. 

### Installation
1. Clone the repository
2. Run `composer install`
3. Run `sail up`
4. Run `sail artisan migrate`
5. Run `sail artisan db:seed`
6. Run `sail npm run dev`
7. Visit `http://localhost` in your browser
8. Profit.

### Radar - process documents
1. Run `php artisan queue:work`
2. Run `php artisan horizon`
3. Upload documents should now process

### Documentation
The application includes comprehensive VitePress documentation covering all domains and APIs.

#### Development
To run the documentation locally:
```bash
npm run docs:dev
```
Documentation will be available at `http://localhost:8081`

#### Production Deployment
Documentation is automatically built and deployed via GitHub Actions when changes are pushed to the main branch.

**Manual build:**
```bash
npm run docs:build
```
Documentation will be available at `/docs` (authentication required)

**GitHub Actions Workflows:**
- **Build Documentation** - Validates documentation builds on PRs
- **Deploy Documentation** - Automatically deploys docs on main branch pushes
- **Rebuild Documentation** - Manual workflow for force rebuilding docs

### Server Requirements
- PHP >= 8.3
- ImageMagick
- TessaractOCR
