<?php

namespace App\Console\Commands;

use App\Domains\SuitabilityReports\Enums\BooleanAnswers;
use App\Domains\SuitabilityReports\Models\SuitabilityReport;
use Illuminate\Console\Command;

class CopyBooleanToEnumAnswers extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'suitability-reports:copy-boolean-to-enum';

    /**
     * The console command description.
     */
    protected $description = 'Copy boolean column values to enum columns';

    /**
     * Boolean columns to copy to BooleanAnswer enum
     */
    private array $booleanColumns = [
        'children_under_18',
        'step_children',
        'child_with_disability_or_care_needs',
        'owns_business',
        'has_will',
        'has_trust',
        'has_lpa',
        'partner_has_will',
        'partner_has_lpa',
    ];

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $reports = SuitabilityReport::all()->collect();

        $bar = $this->output->createProgressBar($reports->count());

        $reports->each(function ($report) use ($bar) {
            $this->info('Updating deal stage for Deal: ' . $report->id);
            $updates = [];
            try {
                foreach ($this->booleanColumns as $column) {
                    $enumColumn = $column . '_enum';
                    $booleanValue = $report->{$column};
                    $enumValue = BooleanAnswers::fromBoolean($booleanValue)->name;
                    $this->info("Updating {$enumColumn} to {$enumValue} for report ID {$report->id}");

                    $updates[$enumColumn] = $enumValue;
                }
                $report->update($updates);
            } catch (\Exception $e) {
                $this->error("Failed to update report ID {$report->id}: {$e->getMessage()}");
            }
            $bar->advance();
        });

        $bar->finish();

        return Command::SUCCESS;
    }
}
