<?php

namespace App\Console\Commands;

use App\Domains\SuitabilityReports\Enums\BooleanAnswers;
use App\Domains\SuitabilityReports\Enums\MarketingPreferences;
use App\Domains\SuitabilityReports\Models\SuitabilityReport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateBooleanToEnumAnswers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'suitability-reports:migrate-boolean-to-enum {--dry-run : Show what would be migrated without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Copy boolean column values to new enum columns in suitability_reports table (preserves original boolean columns)';

    /**
     * Boolean columns to migrate to BooleanAnswer enum
     */
    private array $booleanColumns = [
        'children_under_18',
        'step_children',
        'child_with_disability_or_care_needs',
        'owns_business',
        'has_will',
        'has_trust',
        'has_lpa',
        'partner_has_will',
        'partner_has_lpa',
    ];

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->info('🔍 DRY RUN MODE - No changes will be made');
            $this->newLine();
        }

        // Validate that enum columns exist
        if (!$this->validateEnumColumnsExist()) {
            $this->error('❌ Enum columns do not exist. Please run the migration first.');
            return Command::FAILURE;
        }

        $this->info('🚀 Starting boolean to enum COPY operation for suitability_reports table...');
        $this->info('📋 Original boolean columns will be preserved unchanged');

        if ($isDryRun) {
            $this->showMappingRules();
        }

        $this->newLine();

        // Get total count for progress bar
        $totalReports = SuitabilityReport::count();

        if ($totalReports === 0) {
            $this->info('✅ No suitability reports found. Nothing to migrate.');
            return Command::SUCCESS;
        }

        $this->info("📊 Found {$totalReports} suitability reports to process");
        $this->newLine();

        // Create progress bar
        $progressBar = $this->output->createProgressBar($totalReports);
        $progressBar->setFormat('verbose');

        $migratedCount = 0;
        $errorCount = 0;

        // Process reports in chunks to avoid memory issues
        SuitabilityReport::chunk(100, function ($reports) use ($isDryRun, $progressBar, &$migratedCount, &$errorCount) {
            foreach ($reports as $report) {
                try {
                    $this->migrateReport($report, $isDryRun);
                    $migratedCount++;
                } catch (\Exception $e) {
                    $errorCount++;
                    $this->error("❌ Error migrating report ID {$report->id}: " . $e->getMessage());
                }

                $progressBar->advance();
            }
        });

        $progressBar->finish();
        $this->newLine(2);

        // Show summary
        $this->displaySummary($migratedCount, $errorCount, $isDryRun);

        return $errorCount > 0 ? Command::FAILURE : Command::SUCCESS;
    }

    /**
     * Copy a single report's boolean values to enum columns (preserves original boolean columns)
     */
    private function migrateReport(SuitabilityReport $report, bool $isDryRun): void
    {
        $updates = [];

        // Copy standard boolean columns to BooleanAnswer enum columns
        foreach ($this->booleanColumns as $column) {
            $enumColumn = $column . '_enum';
            $booleanValue = $report->getAttribute($column);
            $enumValue = $this->mapBooleanToEnum($booleanValue);

            // Always set the enum value (even if null maps to 'not_answered')
            $updates[$enumColumn] = $enumValue;

            if ($isDryRun) {
                $this->logColumnMapping($column, $booleanValue, $enumColumn, $enumValue);
            }
        }

        // Special case: copy marketing_preference to MarketingPreferences enum
        $marketingValue = $report->getAttribute('marketing_preference');
        $marketingEnum = $this->mapMarketingPreferenceToEnum($marketingValue);
        $updates['marketing_preference_enum'] = $marketingEnum;

        if ($isDryRun) {
            $this->logColumnMapping('marketing_preference', $marketingValue, 'marketing_preference_enum', $marketingEnum);
        }

        // Apply updates if not dry run
        if (!$isDryRun && !empty($updates)) {
            $report->update($updates);
        }

        // Log summary for this report
        if (!empty($updates)) {
            $this->logReportSummary($report->id, count($updates), $isDryRun);
        }
    }

    /**
     * Map boolean value to BooleanAnswer enum
     */
    private function mapBooleanToEnum(?bool $value): ?string
    {
        return match ($value) {
            true => BooleanAnswers::YES->value,
            false => BooleanAnswers::NO->value,
            null => BooleanAnswers::NOT_ANSWERED->value,
        };
    }

    /**
     * Map marketing preference boolean to MarketingPreferences enum
     */
    private function mapMarketingPreferenceToEnum(?bool $value): ?string
    {
        return match ($value) {
            true => MarketingPreferences::ALL->value,
            null => MarketingPreferences::NONE->value,
            false => MarketingPreferences::NONE->value, // Treat false as none
        };
    }

    /**
     * Log the column mapping during copy operation
     */
    private function logColumnMapping(string $sourceColumn, mixed $sourceValue, string $targetColumn, string $targetValue): void
    {
        $sourceDisplay = $sourceValue === null ? 'null' : ($sourceValue ? 'true' : 'false');
        $this->line("   📋 {$sourceColumn}: {$sourceDisplay} → {$targetColumn}: {$targetValue}");
    }

    /**
     * Log the summary for a report
     */
    private function logReportSummary(int $reportId, int $columnCount, bool $isDryRun): void
    {
        $action = $isDryRun ? 'WOULD COPY' : 'COPIED';
        $this->info("✅ {$action} {$columnCount} columns for Report ID {$reportId}");
        $this->newLine();
    }

    /**
     * Display copy operation summary
     */
    private function displaySummary(int $migratedCount, int $errorCount, bool $isDryRun): void
    {
        $action = $isDryRun ? 'would be processed' : 'processed successfully';

        $this->info("📈 Copy Operation Summary:");
        $this->info("   ✅ Reports {$action}: {$migratedCount}");
        $this->info("   📋 Boolean columns preserved: YES (original data unchanged)");
        $this->info("   🔄 Enum columns populated: YES");

        if ($errorCount > 0) {
            $this->error("   ❌ Reports with errors: {$errorCount}");
        }

        if ($isDryRun) {
            $this->newLine();
            $this->comment('💡 Run without --dry-run to copy the values to enum columns');
            $this->comment('💡 Original boolean columns will remain unchanged');
        } else {
            $this->newLine();
            $this->info('🎉 Copy operation completed successfully!');
            $this->info('📋 All boolean values have been copied to enum columns');
            $this->comment('💡 Original boolean columns are preserved and unchanged');
        }
    }

    /**
     * Validate that all required enum columns exist
     */
    private function validateEnumColumnsExist(): bool
    {
        $tableName = 'suitability_reports';
        $requiredColumns = [];

        // Add all boolean enum columns
        foreach ($this->booleanColumns as $column) {
            $requiredColumns[] = $column . '_enum';
        }

        // Add marketing preference enum column
        $requiredColumns[] = 'marketing_preference_enum';

        // Check if columns exist
        foreach ($requiredColumns as $column) {
            if (!DB::getSchemaBuilder()->hasColumn($tableName, $column)) {
                $this->error("❌ Column '{$column}' does not exist in '{$tableName}' table");
                return false;
            }
        }

        $this->info('✅ All required enum columns exist');
        return true;
    }
}
