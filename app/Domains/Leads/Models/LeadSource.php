<?php

namespace App\Domains\Leads\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LeadSource extends Model
{
    use HasFactory;
    protected $fillable = [
        'name',
        'slug',
        'description',
        'call_slas',
        'max_calls',
        'ranking',
        'enabled',
    ];
    protected $casts = [
        'call_slas' => 'array',
        'enabled' => 'boolean',
    ];
}
