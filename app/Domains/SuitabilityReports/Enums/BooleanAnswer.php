<?php

namespace App\Domains\SuitabilityReports\Enums;

use App\Domains\Global\Enums\EnumToArray;

enum BooleanAnswer: string
{
    use EnumToArray;

    case YES = 'yes';
    case NO = 'no';
    case NOT_ANSWERED = 'not_answered';

    /**
     * Convert from nullable boolean to enum
     */
    public static function fromBoolean(?bool $value): self
    {
        return match ($value) {
            true => self::YES,
            false => self::NO,
            null => self::NOT_ANSWERED,
        };
    }

    /**
     * Convert to nullable boolean
     */
    public function toBoolean(): ?bool
    {
        return match ($this) {
            self::YES => true,
            self::NO => false,
            self::NOT_ANSWERED => null,
        };
    }

    /**
     * Get display label
     */
    public function label(): string
    {
        return match ($this) {
            self::YES => 'Yes',
            self::NO => 'No',
            self::NOT_ANSWERED => 'Not answered',
        };
    }

    /**
     * Check if answered (not null)
     */
    public function isAnswered(): bool
    {
        return $this !== self::NOT_ANSWERED;
    }
}
