<?php

namespace App\Domains\SuitabilityReports\Enums;

use App\Domains\Global\Enums\EnumToArray;

enum MarketingPreferences: string
{
    use EnumToArray;

    case ALL = 'all';
    case EMAIL_ONLY = 'email_only';
    case PHONE_ONLY = 'phone_only';
    case NONE = 'none';

    public static function fromBoolean(bool $value): self|null
    {
        return match ($value) {
            true => self::ALL,
            false => self::NONE,
            default => null,
        };
    }
}
