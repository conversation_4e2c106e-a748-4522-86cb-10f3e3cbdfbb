<?php

namespace App\Domains\SuitabilityReports\Forms;

use App\Domains\SuitabilityReports\Models\SuitabilityReport;
use Livewire\Form;

final class SuitabilityReportForm extends Form
{
    public ?string $relationship_status = null;
    public ?string $partner_name = null;

    public bool $children = false;
    public ?int $number_of_children = null;
    public bool $children_under_18 = false;
    public bool $step_children = false;
    public bool $children_with_disability_or_care_needs = false;

    public bool $property = false;
    public ?int $number_of_properties = null;
    public bool $own_a_business = false;
    public ?string $value_of_estate = null;
    public string $current_will = '';
    public bool $current_will_has_trust = false;

    public bool $lpa = false;
    public bool $has_partner = false;
    public bool $partner_has_will = false;
    public bool $partner_has_lpa = false;
    public bool $marketing_preferences = false;
    public ?string $notes = null;
    public ?string $life_interest_trust = null;
    public ?string $married_with_children_life_trust = null;
    public ?string $children_under_18_will_trust = null;
    public ?string $step_family_life_trust = null;
    public ?string $child_with_disability_trust = null;
    public ?string $property_ownership_iht = null;
    public ?string $business_ownership_iht = null;
    public ?string $value_of_estate_iht = null;
    public ?string $partner_will = null;
    public ?string $partner_lpa = null;

    public function setForm(array $data): void
    {
        $this->fill($data);
    }

    public function setFormFromReport(SuitabilityReport $report): void
    {
        $this->setForm($this->reportToFormArray($report));
    }

    public function update(SuitabilityReport $report): void
    {
        $report->update($this->formToReportArray());
    }

    private function formToReportArray(): array
    {
        return [
            'partner_name' => $this->partner_name,
            'relationship_status' => $this->relationship_status,
            'child_count' => $this->number_of_children ?? 0,
            'children_under_18' => $this->children_under_18,
            'step_children' => $this->step_children,
            'child_with_disability_or_care_needs' => $this->children_with_disability_or_care_needs,
            'property_count' => $this->number_of_properties ?? 0,
            'owns_business' => $this->own_a_business,
            'estate_value' => $this->value_of_estate,
            'has_will' => $this->current_will,
            'has_trust' => $this->current_will_has_trust,
            'has_lpa' => $this->lpa,
            'partner_has_will' => $this->partner_has_will,
            'partner_has_lpa' => $this->partner_has_lpa,
            'marketing_preference' => $this->marketing_preferences,
            'notes' => $this->notes,
            'life_interest_trust' => $this->life_interest_trust,
            'married_with_children_life_trust' => $this->married_with_children_life_trust,
            'children_under_18_will_trust' => $this->children_under_18_will_trust,
            'step_family_life_trust' => $this->step_family_life_trust,
            'child_with_disability_trust' => $this->child_with_disability_trust,
            'property_ownership_iht' => $this->property_ownership_iht,
            'business_ownership_iht' => $this->business_ownership_iht,
            'value_of_estate_iht' => $this->value_of_estate_iht,
            'partner_will' => $this->partner_will,
            'partner_lpa' => $this->partner_lpa,
        ];
    }

    private function reportToFormArray(SuitabilityReport $report): array
    {
        return [
            'relationship_status' => $report->relationship_status,
            'partner_name' => $report->partner_name,
            'children' => (bool) $report->child_count,
            'number_of_children' => $report->child_count,
            'children_under_18' => $report->children_under_18,
            'step_children' => $report->step_children,
            'children_with_disability_or_care_needs' => $report->child_with_disability_or_care_needs,
            'property' => (bool) $report->property_count,
            'number_of_properties' => $report->property_count,
            'own_a_business' => $report->owns_business,
            'value_of_estate' => $report->estate_value,
            'current_will' => $report->has_will,
            'current_will_has_trust' => $report->has_trust,
            'lpa' => $report->has_lpa,
            'has_partner' => in_array($report->relationship_status, ['ENGAGED', 'MARRIED_OR_PARTNER']),
            'partner_has_will' => $report->partner_has_will,
            'partner_has_lpa' => $report->partner_has_lpa,
            'marketing_preferences' => $report->marketing_preference,
            'notes' => $report->notes,
            'life_interest_trust' => $report->life_interest_trust,
            'married_with_children_life_trust' => $report->married_with_children_life_trust,
            'children_under_18_will_trust' => $report->children_under_18_will_trust,
            'step_family_life_trust' => $report->step_family_life_trust,
            'child_with_disability_trust' => $report->child_with_disability_trust,
            'property_ownership_iht' => $report->property_ownership_iht,
            'business_ownership_iht' => $report->business_ownership_iht,
            'value_of_estate_iht' => $report->value_of_estate_iht,
            'partner_will' => $report->partner_will,
            'partner_lpa' => $report->partner_lpa,
        ];
    }
}
