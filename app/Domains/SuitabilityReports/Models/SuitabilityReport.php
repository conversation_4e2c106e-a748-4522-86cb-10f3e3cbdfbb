<?php

namespace App\Domains\SuitabilityReports\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

final class SuitabilityReport extends Model
{
    protected $fillable = [
        'partner_name',
        'partner_date_of_birth',
        'relationship_status',
        'child_count',
        'children_under_18',
        'step_children',
        'child_with_disability_or_care_needs',
        'property_count',
        'owns_business',
        'estate_value',
        'has_will',
        'has_trust',
        'has_lpa',
        'partner_has_will',
        'partner_has_lpa',
        'marketing_preference',
        'notes',
        'life_interest_trust',
        'married_with_children_life_trust',
        'children_under_18_will_trust',
        'step_family_life_trust',
        'child_with_disability_trust',
        'property_ownership_iht',
        'business_ownership_iht',
        'value_of_estate_iht',
        'partner_will',
        'partner_lpa',
    ];

    protected $casts = [
        'partner_date_of_birth' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $with = [
        'suitabilityReportCustomer',
    ];

    /**
     * Get the customer for this SuitabilityReport
     */
    public function suitabilityReportCustomer(): BelongsTo
    {
        return $this->belongsTo(related: SuitabilityReportCustomer::class, ownerKey: 'customer_id');
    }

    /**
     * Get the customer for this SuitabilityReport
     */
    public function recommendation(): HasOne
    {
        return $this->hasOne(SuitabilityReportRecommendation::class);
    }
}
