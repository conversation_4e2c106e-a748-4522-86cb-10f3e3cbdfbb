<?php

namespace App\Domains\SuitabilityReports\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

final class SuitabilityReport extends Model
{
    protected $fillable = [
        'partner_name',
        'partner_date_of_birth',
        'relationship_status',
        'child_count',
        'children_under_18',
        'children_under_18_enum',
        'step_children',
        'step_children_enum',
        'child_with_disability_or_care_needs',
        'child_with_disability_or_care_needs_enum',
        'property_count',
        'owns_business',
        'owns_business_enum',
        'estate_value',
        'has_will',
        'has_will_enum',
        'has_trust',
        'has_trust_enum',
        'has_lpa',
        'has_lpa_enum',
        'partner_has_will',
        'partner_has_will_enum',
        'partner_has_lpa',
        'partner_has_lpa_enum',
        'marketing_preference',
        'marketing_preference_enum',
        'notes',
        'life_interest_trust',
        'married_with_children_life_trust',
        'children_under_18_will_trust',
        'step_family_life_trust',
        'child_with_disability_trust',
        'property_ownership_iht',
        'business_ownership_iht',
        'value_of_estate_iht',
        'partner_will',
        'partner_lpa',
    ];

    protected $casts = [
        'partner_date_of_birth' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $with = [
        'suitabilityReportCustomer',
    ];

    /**
     * Get the customer for this SuitabilityReport
     */
    public function suitabilityReportCustomer(): BelongsTo
    {
        return $this->belongsTo(related: SuitabilityReportCustomer::class, ownerKey: 'customer_id');
    }

    /**
     * Get the customer for this SuitabilityReport
     */
    public function recommendation(): HasOne
    {
        return $this->hasOne(SuitabilityReportRecommendation::class);
    }
}
