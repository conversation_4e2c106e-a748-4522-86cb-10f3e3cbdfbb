<?php

namespace App\Domains\SuitabilityReports\Synths;

use App\Domains\SuitabilityReports\Forms\SuitabilityReportForm;
use Livewire\Mechanisms\HandleComponents\Synthesizers\Synth;

class SuitabilityReportFormSynth extends Synth
{
    public static string $key = 'suitability_report_form';

    public static function match($target): bool
    {
        return $target instanceof SuitabilityReportForm;
    }

    public function dehydrate(SuitabilityReportForm $target): array
    {
        // Don't convert false to empty string - preserve boolean values
        $data = array_map(function ($val) {
            if ($val === null) {
                return '';
            }
            // Preserve boolean false values
            if ($val === false) {
                return false;
            }
            return $val;
        }, $target->all());

        return [$data, []];
    }

    public function hydrate($value): SuitabilityReportForm
    {
        $component = $this->context->component;

        $formData = new SuitabilityReportForm($component, 'form');

        // Only convert empty strings to null, preserve boolean false
        $clean = array_map(function ($val) {
            if ($val === '' || $val === null) {
                return null;
            }
            // Preserve boolean false values
            if ($val === false) {
                return false;
            }
            return $val;
        }, $value);

        $formData->setForm($clean);

        return $formData;
    }

    public function get(&$target, $key): mixed
    {
        return $target->{$key};
    }

    public function set(&$target, $key, $value): void
    {
        // Handle boolean false explicitly
        if ($value === false) {
            $target->{$key} = false;
        } elseif (is_string($value) && (trim($value) === '' || $value === '')) {
            $target->{$key} = null;
        } else {
            $target->{$key} = $value;
        }
    }
}
