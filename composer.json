{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.4", "ext-imagick": "*", "aws/aws-sdk-php": "^3.342", "hubspot/api-client": "^12.1", "laravel/framework": "^12.0", "laravel/horizon": "^5.30", "laravel/prompts": "^0.3.5", "laravel/pulse": "^1.4", "laravel/socialite": "^5.18", "laravel/telescope": "^5.5", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.29", "livewire/flux": "^2.0", "livewire/flux-pro": "^2.0", "livewire/livewire": "^3.4", "livewire/volt": "^1.0", "maatwebsite/excel": "^3.1", "openai-php/laravel": "^0.11", "pagevamp/laravel-cloudwatch-logs": "^1.1", "sentry/sentry-laravel": "^4.13", "socialiteproviders/google": "^4.1", "spatie/laravel-markdown": "^2.7", "spatie/laravel-tags": "^4.9", "spatie/pdf-to-image": "^3.1", "thiagoalessio/tesseract_ocr": "^2.13"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/breeze": "^2.3", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "pestphp/pest": "^3.7", "pestphp/pest-plugin-laravel": "^3.1", "spatie/laravel-ray": "^1.40", "tightenco/duster": "^3.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"flux-pro": {"type": "composer", "url": "https://composer.fluxui.dev"}}}