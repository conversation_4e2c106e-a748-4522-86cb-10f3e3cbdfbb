<?php

namespace Database\Factories\Domains\Leads\Models;

use App\Domains\Leads\Models\Deal;
use App\Domains\Leads\Models\LeadSource;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class DealFactory extends Factory
{
    protected $model = Deal::class;

    public function definition()
    {
        $lead_sources = LeadSource::all()->pluck('name');

        return [
            'deal_object_id' => $this->faker->unique()->numerify('#########'),
            'lead_source' => $this->faker->randomElement($lead_sources),
            'lead_gen' => $this->faker->randomElement(['Law Superstore', 'MVP', 'Crystal Data Leads']),
            'marketing_status' => $this->faker->randomElement(['Ringfenced for send', 'Sent']),
            'deal_name' => $this->faker->name,
            'deal_stage' => $this->faker->randomElement(config('deal_stages.stage_ids')),
            'deal_created_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'property_changed_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'call_slas' => [
                $this->faker->dateTimeBetween('now', '+2 years')->format('Y-m-d\TH:i:s.u\Z'),
                $this->faker->dateTimeBetween('now', '+2 years')->format('Y-m-d\TH:i:s.u\Z'),
                $this->faker->dateTimeBetween('now', '+2 years')->format('Y-m-d\TH:i:s.u\Z'),
            ],
            'call_count' => $this->faker->numberBetween(0, 12),
            'created_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
            'updated_at' => Carbon::now(),
        ];
    }
}
