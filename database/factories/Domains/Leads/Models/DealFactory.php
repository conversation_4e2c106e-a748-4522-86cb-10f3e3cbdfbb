<?php

namespace Database\Factories\Domains\Leads\Models;

use App\Domains\Leads\Models\Deal;
use App\Domains\Leads\Models\LeadSource;
use App\Domains\Leads\Services\SlaCalculationService;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class DealFactory extends Factory
{
    protected $model = Deal::class;

    public function definition()
    {
        $lead_sources = LeadSource::all();

        if ($lead_sources->isEmpty()) {
            // Fallback if no lead sources exist
            $leadSource = 'Test Source';
            $maxCalls = 3;
        } else {
            $selectedSource = $this->faker->randomElement($lead_sources);
            $leadSource = $selectedSource->name;
            $maxCalls = $selectedSource->max_calls ?? 3;
        }

        // Generate realistic call count (0 to max_calls - 1)
        $callCount = $this->faker->numberBetween(0, max(0, $maxCalls - 1));

        // Generate realistic deal creation time (last 30 days)
        $dealCreatedAt = Carbon::instance($this->faker->dateTimeBetween('-30 days', 'now'));

        // Generate call SLAs using the SLA calculation service for realism
        $callSlas = $this->generateRealisticCallSlas($dealCreatedAt, $maxCalls);

        return [
            'deal_object_id' => $this->faker->unique()->numerify('#########'),
            'lead_source' => $leadSource,
            'lead_gen' => $this->faker->randomElement(['Law Superstore', 'MVP', 'Crystal Data Leads']),
            'marketing_status' => $this->faker->randomElement(['Ringfenced for send', 'Sent']),
            'deal_name' => $this->faker->name . ' - ' . $this->faker->company,
            'deal_stage' => $this->faker->randomElement(config('deal_stages.stage_ids')),
            'deal_created_at' => $dealCreatedAt,
            'property_changed_at' => $this->faker->dateTimeBetween($dealCreatedAt, 'now'),
            'call_slas' => $callSlas,
            'call_count' => $callCount,
            'ranking' => $this->faker->numberBetween(1, 100), // Add realistic ranking
            'created_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
            'updated_at' => Carbon::now(),
        ];
    }

    /**
     * Create a deal that is definitely breached
     */
    public function breached(): static
    {
        return $this->state(function (array $attributes) {
            $callCount = $this->faker->numberBetween(0, 2);
            $dealCreatedAt = Carbon::instance($this->faker->dateTimeBetween('-7 days', '-1 day'));

            // Generate SLAs that are definitely in the past
            $callSlas = [];
            for ($i = 0; $i <= $callCount + 1; $i++) {
                $hoursAgo = ($i + 1) * 2; // 2, 4, 6 hours ago
                $callSlas[] = Carbon::now()->subHours($hoursAgo)->toISOString();
            }

            return [
                'deal_created_at' => $dealCreatedAt,
                'call_slas' => $callSlas,
                'call_count' => $callCount,
                'ranking' => $this->faker->numberBetween(80, 100), // High priority for breached deals
            ];
        });
    }

    /**
     * Create a deal that is definitely within SLA
     */
    public function withinSla(): static
    {
        return $this->state(function (array $attributes) {
            $callCount = $this->faker->numberBetween(0, 2);
            $dealCreatedAt = Carbon::instance($this->faker->dateTimeBetween('-1 day', 'now'));

            // Generate SLAs that are definitely in the future
            $callSlas = [];
            for ($i = 0; $i <= $callCount + 1; $i++) {
                $hoursFromNow = ($i + 1) * 2; // 2, 4, 6 hours from now
                $callSlas[] = Carbon::now()->addHours($hoursFromNow)->toISOString();
            }

            return [
                'deal_created_at' => $dealCreatedAt,
                'call_slas' => $callSlas,
                'call_count' => $callCount,
                'ranking' => $this->faker->numberBetween(1, 50), // Lower priority for non-urgent deals
            ];
        });
    }

    /**
     * Generate realistic call SLAs with a mix of breached and non-breached deals
     */
    private function generateRealisticCallSlas(Carbon $dealCreatedAt, int $maxCalls): array
    {
        $slaService = new SlaCalculationService;
        $callSlas = [];

        // Use realistic SLA minutes based on common lead sources
        $slaMinutes = [
            5,    // 5 minutes (urgent leads like Bespoke)
            185,  // ~3 hours (second call)
            1085, // ~18 hours (third call)
            2345, // ~39 hours (fourth call)
        ];

        $baseTime = $dealCreatedAt;

        for ($i = 0; $i < $maxCalls; $i++) {
            $minutes = $slaMinutes[$i] ?? 1440; // Default to 24 hours if no SLA defined

            // 30% chance to make this SLA breached for realistic test data
            if ($this->faker->boolean(30)) {
                // Make it breached by subtracting additional time
                $breachOffset = $this->faker->numberBetween(60, 1440); // 1-24 hours ago
                $slaTime = $slaService->calculateCallDeadline($minutes, $baseTime);
                $slaTime = $slaTime->subMinutes($breachOffset);
            } else {
                // Normal SLA calculation
                $slaTime = $slaService->calculateCallDeadline($minutes, $baseTime);
            }

            $callSlas[] = $slaTime->toISOString();
            $baseTime = $slaTime; // Next SLA builds on previous
        }

        return $callSlas;
    }
}
