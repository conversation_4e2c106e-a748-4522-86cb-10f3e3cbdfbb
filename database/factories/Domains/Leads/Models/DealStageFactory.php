<?php

namespace Database\Factories\Domains\Leads\Models;

use App\Domains\Leads\Models\DealStage;
use Illuminate\Database\Eloquent\Factories\Factory;

class DealStageFactory extends Factory
{
    protected $model = DealStage::class;

    public function definition(): array
    {
        return [
            'stage_id' => $this->faker->unique()->numerify('########'),
            'label' => $this->faker->words(3, true),
        ];
    }
}
