<?php

namespace Database\Factories\Domains\Leads\Models;

use App\Domains\Leads\Models\LeadSource;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class LeadSourceFactory extends Factory
{
    protected $model = LeadSource::class;

    public function definition(): array
    {
        $name = $this->faker->company . ' ' . $this->faker->randomElement(['Leads', 'Referrals', 'Online', 'Direct']);

        return [
            'name' => $name,
            'slug' => Str::slug($name),
            'description' => $this->faker->sentence(),
            'ranking' => $this->faker->numberBetween(1, 10),
            'call_slas' => [
                $this->faker->numberBetween(5, 60),    // First call: 5-60 minutes
                $this->faker->numberBetween(120, 480), // Second call: 2-8 hours
                $this->faker->numberBetween(1440, 2880), // Third call: 1-2 days
            ],
            'max_calls' => 3,
            'enabled' => true,
        ];
    }

    /**
     * Create a high-priority lead source (urgent)
     */
    public function urgent(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'ranking' => 1,
                'call_slas' => [5, 185, 1085], // Very short SLAs like Bespoke
            ];
        });
    }

    /**
     * Create a low-priority lead source
     */
    public function lowPriority(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'ranking' => $this->faker->numberBetween(6, 10),
                'call_slas' => [1440], // 24 hours only
                'max_calls' => 1,
            ];
        });
    }

    /**
     * Create a disabled lead source
     */
    public function disabled(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'enabled' => false,
            ];
        });
    }
}
