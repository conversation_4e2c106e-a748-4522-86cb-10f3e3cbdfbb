<?php

namespace Database\Factories\Domains\Radar\Models;

use App\Domains\Radar\Enums\DocumentType;
use App\Domains\Radar\Models\Document;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

/**
 * @extends Factory<Document>
 */
class DocumentFactory extends Factory
{
    protected $model = Document::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $ref = 'ZZ' . Str::padLeft((string) $this->faker->numberBetween(1, 9999), 8, '0');
        $type = DocumentType::WILL->value;

        Storage::fake('local');

        UploadedFile::fake()->create("{$ref}.pdf", 1, 'application/pdf');

        return [
            'reference' => $ref,
            'type' => $type,
            'disk' => 'local',
            'path' => 'documents/' . $type . '/' . $ref . '.pdf',
            'filename' => $ref . '.pdf',
            'extension' => 'pdf',
        ];
    }
}
