<?php

namespace Database\Factories\Domains\Radar\Models;

use App\Domains\Radar\Models\Document;
use App\Domains\Radar\Models\RadarResult;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class RadarResultFactory extends Factory
{
    protected $model = RadarResult::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => $this->faker->uuid(),
            'user_id' => User::factory()->create()->id,
            'document_id' => Document::factory()->create()->id,
            'notes' => $this->faker->optional()->paragraphs(3, true),
        ];
    }
}
