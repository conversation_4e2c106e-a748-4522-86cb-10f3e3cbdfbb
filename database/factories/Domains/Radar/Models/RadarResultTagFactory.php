<?php

namespace Database\Factories\Domains\Radar\Models;

use App\Domains\Radar\Models\RadarResultTag;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class RadarResultTagFactory extends Factory
{
    protected $model = RadarResultTag::class;

    public function definition(): array
    {
        return [
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
