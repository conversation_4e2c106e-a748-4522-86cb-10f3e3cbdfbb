<?php

use App\Domains\Radar\Enums\RadarStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('radar_results', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('user_id')->constrained();
            $table->foreignUlid('document_id')->constrained();

            $table->string('batch_id')->nullable();

            $table->text('ocr_text')->nullable();
            $table->text('summary')->nullable();

            $table->boolean('ocr_complete')->default(false);
            $table->boolean('tag_extraction_complete')->default(false);
            $table->boolean('insight_generation_complete')->default(false);
            $table->boolean('summary_generation_complete')->default(false);
            $table->boolean('scores_calculated')->default(false);

            $table->integer('score')->default(0);
            $table->json('scores')->nullable();

            $table->text('ollie_thinks')->nullable();

            $table->string('status')->default(RadarStatus::PENDING);
            $table->text('notes')->nullable();

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('radar_results');
    }
};
