<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('radar_result_tags', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('radar_result_id')->constrained();
            $table->foreignUlid('radar_tag_id')->constrained();
            $table->text('value')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('radar_result_tags');
    }
};
