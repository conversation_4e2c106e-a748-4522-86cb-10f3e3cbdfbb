<?php

use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('password');
            $table->dropColumn('email_verified_at');
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('password')->nullable();
            $table->timestamp('email_verified_at')->nullable();
        });
        // Fill user table passwords with default password
        User::update([
            'password' => '$2y$12$rqpFssDEDlNHp9HEcR2useWs09jUkEsAKgu5PrGFsDFnR2MqtsQIS', // 'password'
        ]);
    }
};
