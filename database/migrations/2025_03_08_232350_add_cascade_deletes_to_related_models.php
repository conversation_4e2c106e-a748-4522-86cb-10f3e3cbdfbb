<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('radar_result_tags', function (Blueprint $table) {
            $table->dropForeign(['radar_result_id']); // Remove existing foreign key
            $table->foreign('radar_result_id')->references('id')->on('radar_results')->onDelete('cascade');
        });

        Schema::table('radar_result_insights', function (Blueprint $table) {
            $table->dropForeign(['radar_result_id']);
            $table->foreign('radar_result_id')->references('id')->on('radar_results')->onDelete('cascade');
        });

    }

    public function down()
    {
        Schema::table('radar_result_tags', function (Blueprint $table) {
            $table->dropForeign(['radar_result_id']);
            $table->foreign('radar_result_id')->references('id')->on('radar_results');
        });

        Schema::table('radar_result_insights', function (Blueprint $table) {
            $table->dropForeign(['radar_result_id']);
            $table->foreign('radar_result_id')->references('id')->on('radar_results');
        });
    }
};
