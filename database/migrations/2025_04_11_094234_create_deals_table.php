<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('leads_deals', function (Blueprint $table) {
            $table->id();
            $table->string('deal_object_id')->unique();
            $table->string('lead_source')->nullable();
            $table->string('lead_gen')->nullable();
            $table->string('marketing_status')->nullable();
            $table->string('deal_name')->nullable();
            $table->string('deal_stage')->nullable();
            $table->timestamp('deal_created_at')->nullable();
            $table->timestamp('property_changed_at')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('leads_deals');
    }
};
