<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('leads_calls', function (Blueprint $table) {
            $table->id();
            $table->string('call_object_id')->unique();
            $table->foreignId('deal_id')->nullable()->references('id')->on('leads_deals')->onDelete('cascade');
            $table->timestamp('called_at')->nullable();
            $table->string('call_direction')->nullable();
            $table->integer('call_duration')->nullable();
            $table->string('call_status')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('leads_calls');
    }
};
