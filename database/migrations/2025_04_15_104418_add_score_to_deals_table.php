<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('leads_deals', function (Blueprint $table) {
            $table->integer('ranking')->default(0);
        });
    }

    public function down(): void
    {
        Schema::table('leads_deals', function (Blueprint $table) {
            $table->dropColumn('ranking');
        });
    }
};
