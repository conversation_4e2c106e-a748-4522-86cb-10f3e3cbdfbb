<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('leads_deals', function (Blueprint $table) {
            $table->json('call_slas')->nullable();
            $table->integer('max_calls')->default(0);
        });
    }

    public function down(): void
    {
        Schema::table('deals', function (Blueprint $table) {
            $table->dropColumn('call_slas');
            $table->dropColumn('max_calls');
        });
    }
};
