<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('suitability_report_customers', function (Blueprint $table): void {
            $table->id();
            $table->string('name');
            $table->string('hubspot_id')->nullable();
            $table->string('nova_id')->nullable();
            $table->foreignUlid('estate_planner_id')->constrained('users', 'id')->cascadeOnDelete();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('suitability_report_customers');
    }
};
