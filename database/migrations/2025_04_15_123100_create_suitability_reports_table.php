<?php

use App\Domains\SuitabilityReports\Enums\EstateValue;
use App\Domains\SuitabilityReports\Enums\RelationshipStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('suitability_reports', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('customer_id')->constrained('suitability_report_customers', 'id')->cascadeOnDelete();
            $table->enum('relationship_status', RelationshipStatus::names());
            $table->string('partner_name')->nullable();
            $table->smallInteger(column: 'child_count', unsigned: true)->default(0);
            $table->boolean('children_under_18')->default(false);
            $table->boolean('step_children')->default(false);
            $table->boolean('child_with_disability_or_care_needs')->default(false);
            $table->smallInteger(column: 'property_count', unsigned: true)->default(0);
            $table->boolean('owns_business')->default(false);
            $table->enum('estate_value', EstateValue::names());
            $table->boolean('has_will')->default(false);
            $table->boolean('has_trust')->default(false);
            $table->boolean('has_lpa')->default(false);
            $table->boolean('partner_has_will')->default(false);
            $table->boolean('partner_has_lpa')->default(false);
            $table->boolean('marketing_preference')->default(false);
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('suitability_reports');
    }
};
