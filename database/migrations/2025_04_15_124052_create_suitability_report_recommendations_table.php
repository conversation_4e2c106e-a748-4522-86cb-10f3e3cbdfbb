<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('suitability_report_recommendations', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('suitability_report_customer_id')->constrained()->cascadeOnDelete();
            $table->foreignId('suitability_report_id')->constrained()->cascadeOnDelete();
            $table->boolean('will')->default(false);
            $table->boolean('trusts')->default(false);
            $table->boolean('lpa')->default(false);
            $table->boolean('iht')->default(false);
            $table->string('will_writer')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('suitability_report_recommendations');
    }
};
