<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('lead_sources', function (Blueprint $table) {
            $table->dropColumn([
                'first_call_sla',
                'second_call_sla',
                'third_call_sla',
            ]);
            $table->json('call_slas')->nullable();
            $table->integer('max_calls')->default(0);
        });
    }

    public function down(): void
    {
        Schema::table('lead_sources', function (Blueprint $table) {
            $table->dropColumn([
                'call_slas',
                'max_calls',
            ]);
            $table->integer('first_call_sla')->nullable();
            $table->integer('second_call_sla')->nullable();
            $table->integer('third_call_sla')->nullable();
        });
    }
};
