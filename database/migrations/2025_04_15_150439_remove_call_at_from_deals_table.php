<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('leads_deals', function (Blueprint $table) {
            $table->dropColumn('call_by');
            $table->dropColumn('call_attempts');
            $table->dropColumn('max_calls');
        });
    }

    public function down(): void
    {
        Schema::table('leads_deals', function (Blueprint $table) {
            $table->timestamp('call_by')->nullable();
            $table->integer('call_attempts')->default(0);
            $table->integer('max_calls')->default(0);
        });
    }
};
