<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('lead_sources', function (Blueprint $table) {
            $table->dropColumn('weight');
            $table->float('ranking')->default(0);
        });
    }

    public function down(): void
    {
        Schema::table('float', function (Blueprint $table) {
            $table->dropColumn('ranking');
            $table->integer('weight')->default(0);
        });
    }
};
