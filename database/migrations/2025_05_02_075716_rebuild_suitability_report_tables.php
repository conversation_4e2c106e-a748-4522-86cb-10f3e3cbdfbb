<?php

use App\Domains\SuitabilityReports\Enums\EstateValue;
use App\Domains\SuitabilityReports\Enums\RelationshipStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        DB::statement('DROP TABLE suitability_report_recommendations');
        DB::statement('DROP TABLE suitability_reports');
        DB::statement('DROP TABLE suitability_report_customers');

        Schema::create('suitability_report_customers', function (Blueprint $table): void {
            $table->ulid('id')->primary();
            $table->string('name');
            $table->string('hubspot_id')->nullable();
            $table->string('nova_id')->nullable();
            $table->foreignUlid('estate_planner_id')->nullable()->constrained('users', 'id')->nullOnDelete();
            $table->timestamps();
        });

        Schema::create('suitability_reports', function (Blueprint $table): void {
            $table->id();
            $table->foreignUlid('customer_id')->constrained('suitability_report_customers')->cascadeOnDelete();
            $table->enum('relationship_status', RelationshipStatus::names())->nullable();
            $table->string('partner_name')->nullable();
            $table->smallInteger(column: 'child_count', unsigned: true)->default(0);
            $table->boolean('children_under_18')->default(false);
            $table->boolean('step_children')->default(false);
            $table->boolean('child_with_disability_or_care_needs')->default(false);
            $table->smallInteger(column: 'property_count', unsigned: true)->default(0);
            $table->boolean('owns_business')->default(false);
            $table->enum('estate_value', EstateValue::names())->nullable();
            $table->boolean('has_will')->default(false);
            $table->boolean('has_trust')->default(false);
            $table->boolean('has_lpa')->default(false);
            $table->boolean('partner_has_will')->default(false);
            $table->boolean('partner_has_lpa')->default(false);
            $table->boolean('marketing_preference')->default(false);
            $table->text('notes')->nullable();
            $table->timestamps();
        });

        Schema::create('suitability_report_recommendations', function (Blueprint $table): void {
            $table->id();
            $table->foreignUlid('suitability_report_customer_id')->constrained('suitability_report_customers')->cascadeOnDelete();
            $table->foreignId('suitability_report_id')->constrained()->cascadeOnDelete();
            $table->boolean('will')->default(false);
            $table->boolean('trusts')->default(false);
            $table->boolean('lpa')->default(false);
            $table->boolean('iht')->default(false);
            $table->string('will_writer')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        DB::statement('DROP TABLE suitability_report_recommendations');
        DB::statement('DROP TABLE suitability_reports');
        DB::statement('DROP TABLE suitability_report_customers');
    }
};
