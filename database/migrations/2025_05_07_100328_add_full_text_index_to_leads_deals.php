<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('leads_deals', function (Blueprint $table) {
            if (! $this->isSqlite()) {
                $table->fullText('deal_name');
            }
            $table->index('lead_source');
        });
    }

    public function down(): void
    {
        Schema::table('leads_deals', function (Blueprint $table) {
            $table->dropFullText('leads_deals_deal_name_fulltext');
            $table->dropIndex(['lead_source']);
        });
    }

    protected function isSqlite(): bool
    {
        return Schema::connection($this->getConnection())
            ->getConnection()
            ->getPdo()
            ->getAttribute(PDO::ATTR_DRIVER_NAME) === 'sqlite';
    }
};
