<?php

use App\Domains\SuitabilityReports\Enums\CustomerInterestResponses;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('suitability_reports', function (Blueprint $table) {
            $table->enum('life_interest_trust', CustomerInterestResponses::names())->nullable();
            $table->enum('married_with_children_life_trust', CustomerInterestResponses::names())->nullable();
            $table->enum('children_under_18_will_trust', CustomerInterestResponses::names())->nullable();
            $table->enum('step_family_life_trust', CustomerInterestResponses::names())->nullable();
            $table->enum('child_with_disability_trust', CustomerInterestResponses::names())->nullable();
            $table->enum('property_ownership_iht', CustomerInterestResponses::names())->nullable();
            $table->enum('business_ownership_iht', CustomerInterestResponses::names())->nullable();
            $table->enum('value_of_estate_iht', CustomerInterestResponses::names())->nullable();
            $table->enum('partner_will', CustomerInterestResponses::names())->nullable();
            $table->enum('partner_lpa', CustomerInterestResponses::names())->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('suitability_reports', function (Blueprint $table) {
            $table->dropColumn('life_interest_trust');
            $table->dropColumn('married_with_children_life_trust');
            $table->dropColumn('children_under_18_will_trust');
            $table->dropColumn('step_family_life_trust');
            $table->dropColumn('child_with_disability_trust');
            $table->dropColumn('property_ownership_iht');
            $table->dropColumn('business_ownership_iht');
            $table->dropColumn('value_of_estate_iht');
            $table->dropColumn('partner_will');
            $table->dropColumn('partner_lpa');
        });
    }
};
