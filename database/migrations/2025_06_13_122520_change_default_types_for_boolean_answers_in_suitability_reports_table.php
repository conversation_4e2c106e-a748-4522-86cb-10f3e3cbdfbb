<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('suitability_reports', function (Blueprint $table) {
            // Make all boolean columns nullable but keep existing defaults
            // This preserves existing false values while allowing null for new records
            $table->boolean('children_under_18')->nullable()->change();
            $table->boolean('step_children')->nullable()->change();
            $table->boolean('child_with_disability_or_care_needs')->nullable()->change();
            $table->boolean('owns_business')->nullable()->change();
            $table->boolean('has_will')->nullable()->change();
            $table->boolean('has_trust')->nullable()->change();
            $table->boolean('has_lpa')->nullable()->change();
            $table->boolean('partner_has_will')->nullable()->change();
            $table->boolean('partner_has_lpa')->nullable()->change();
            $table->boolean('marketing_preference')->nullable()->change();

            // Add new partner_date_of_birth column
            $table->date('partner_date_of_birth')->nullable()->after('partner_name');
        });
    }

    public function down(): void
    {
        Schema::table('suitability_reports', function (Blueprint $table) {
            // Convert null values back to false before restoring non-nullable constraint
            DB::statement('UPDATE suitability_reports SET children_under_18 = false WHERE children_under_18 IS NULL');
            DB::statement('UPDATE suitability_reports SET step_children = false WHERE step_children IS NULL');
            DB::statement('UPDATE suitability_reports SET child_with_disability_or_care_needs = false WHERE child_with_disability_or_care_needs IS NULL');
            DB::statement('UPDATE suitability_reports SET owns_business = false WHERE owns_business IS NULL');
            DB::statement('UPDATE suitability_reports SET has_will = false WHERE has_will IS NULL');
            DB::statement('UPDATE suitability_reports SET has_trust = false WHERE has_trust IS NULL');
            DB::statement('UPDATE suitability_reports SET has_lpa = false WHERE has_lpa IS NULL');
            DB::statement('UPDATE suitability_reports SET partner_has_will = false WHERE partner_has_will IS NULL');
            DB::statement('UPDATE suitability_reports SET partner_has_lpa = false WHERE partner_has_lpa IS NULL');
            DB::statement('UPDATE suitability_reports SET marketing_preference = false WHERE marketing_preference IS NULL');

            // Restore boolean columns with their original defaults and non-nullable constraint
            $table->boolean('children_under_18')->default(false)->change();
            $table->boolean('step_children')->default(false)->change();
            $table->boolean('child_with_disability_or_care_needs')->default(false)->change();
            $table->boolean('owns_business')->default(false)->change();
            $table->boolean('has_will')->default(false)->change();
            $table->boolean('has_trust')->default(false)->change();
            $table->boolean('has_lpa')->default(false)->change();
            $table->boolean('partner_has_will')->default(false)->change();
            $table->boolean('partner_has_lpa')->default(false)->change();
            $table->boolean('marketing_preference')->default(false)->change();

            // Remove the new column
            $table->dropColumn('partner_date_of_birth');
        });
    }
};
