<?php

use App\Domains\SuitabilityReports\Enums\BooleanAnswers;
use App\Domains\SuitabilityReports\Enums\MarketingPreferences;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('suitability_reports', function (Blueprint $table) {
            // Add new enum columns with _enum suffix for all boolean fields
            // These will replace the existing boolean columns in the next phase

            // Children-related boolean questions
            $table->enum('children_under_18_enum', BooleanAnswers::names())->nullable()->after('children_under_18');
            $table->enum('step_children_enum', BooleanAnswers::names())->nullable()->after('step_children');
            $table->enum('child_with_disability_or_care_needs_enum', BooleanAnswers::names())->nullable()->after('child_with_disability_or_care_needs');

            // Business and property boolean questions
            $table->enum('owns_business_enum', BooleanAnswers::names())->nullable()->after('owns_business');

            // Legal documents boolean questions
            $table->enum('has_will_enum', BooleanAnswers::names())->nullable()->after('has_will');
            $table->enum('has_trust_enum', BooleanAnswers::names())->nullable()->after('has_trust');
            $table->enum('has_lpa_enum', BooleanAnswers::names())->nullable()->after('has_lpa');

            // Partner-related boolean questions
            $table->enum('partner_has_will_enum', BooleanAnswers::names())->nullable()->after('partner_has_will');
            $table->enum('partner_has_lpa_enum', BooleanAnswers::names())->nullable()->after('partner_has_lpa');

            // Marketing preferences - special case using MarketingPreferences enum
            $table->enum('marketing_preference_enum', MarketingPreferences::names())->nullable()->after('marketing_preference');
        });
    }

    public function down(): void
    {
        Schema::table('suitability_reports', function (Blueprint $table) {
            // Drop all the new enum columns
            $table->dropColumn([
                'children_under_18_enum',
                'step_children_enum',
                'child_with_disability_or_care_needs_enum',
                'owns_business_enum',
                'has_will_enum',
                'has_trust_enum',
                'has_lpa_enum',
                'partner_has_will_enum',
                'partner_has_lpa_enum',
                'marketing_preference_enum',
            ]);
        });
    }
};
