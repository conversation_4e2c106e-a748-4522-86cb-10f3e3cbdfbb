<?php

namespace Database\Seeders;

use App\Domains\Leads\Models\Deal;
use App\Models\User;
use Illuminate\Database\Seeder;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        User::factory()->create([
            'name' => 'OL Dev',
            'email' => '<EMAIL>',
        ]);

        $this->call([
            RadarTagSeeder::class,
            RadarInsightSeeder::class,
            LeadSourceSeeder::class,
            DealStageSeeder::class,
        ]);

        // Create a realistic mix of deals for testing the dashboard
        Deal::factory(30)->create(); // Random mix (30% will be breached due to factory logic)
        Deal::factory(10)->breached()->create(); // Definitely breached deals
        Deal::factory(10)->withinSla()->create(); // Definitely within SLA deals

    }
}
