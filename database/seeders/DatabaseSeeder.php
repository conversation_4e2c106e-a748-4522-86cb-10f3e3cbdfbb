<?php

namespace Database\Seeders;

use App\Domains\Leads\Models\Deal;
use App\Models\User;
use Illuminate\Database\Seeder;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        User::factory()->create([
            'name' => 'OL Dev',
            'email' => '<EMAIL>',
        ]);

        $this->call([
            RadarTagSeeder::class,
            RadarInsightSeeder::class,
            LeadSourceSeeder::class,
            DealStageSeeder::class,
        ]);

        Deal::factory(50)->create();

    }
}
