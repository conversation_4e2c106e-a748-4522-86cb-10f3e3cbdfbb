<?php

namespace Database\Seeders;

use App\Domains\Leads\Models\DealStage;
use Faker\Generator;
use Illuminate\Database\Seeder;

class DealStageSeeder extends Seeder
{
    public function __construct(private Generator $faker) {}

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $stages = array_map(fn ($stage): array => [
            'stage_id' => $stage,
            'label' => $this->faker->words(3, true),
        ], config('deal_stages.stage_ids'));

        DealStage::insert($stages);
    }
}
