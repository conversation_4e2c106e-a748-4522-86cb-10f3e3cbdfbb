<?php

namespace Database\Seeders;

use App\Domains\Leads\Models\LeadSource;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class LeadSourceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sources = array_map(
            fn ($source): array => [
                'name' => $source['source'],
                'slug' => Str::slug($source['source']),
                'ranking' => $source['ranking'] ?? 0,
                'call_slas' => json_encode($source['call_slas'] ?? []),
                'max_calls' => count($source['call_slas']) ?? 1,
            ],
            json_decode(file_get_contents(resource_path('data/lead_sources.json')), true)
        );

        LeadSource::insert($sources);
    }
}
