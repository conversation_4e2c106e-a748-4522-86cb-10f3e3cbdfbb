<?php

namespace Database\Seeders;

use App\Domains\Radar\Models\RadarInsight;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class RadarInsightSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tags = json_decode(file_get_contents(resource_path('data/insights_initial.json')), true);

        foreach ($tags as $tag) {

            RadarInsight::create([
                'slug' => Str::slug(Arr::get($tag, 'name'), '_'),
                'name' => Arr::get($tag, 'name'),
                'description' => Arr::get($tag, 'description'),
                'signal' => Arr::get($tag, 'signal'),
                'example' => Arr::get($tag, 'example'),
            ]);
        }
    }
}
