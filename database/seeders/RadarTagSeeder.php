<?php

namespace Database\Seeders;

use App\Domains\Radar\Models\RadarTag;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class RadarTagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tags = json_decode(file_get_contents(resource_path('data/radar_tags_initial.json')), true);

        foreach ($tags as $tag) {

            RadarTag::create([
                'slug' => Str::slug(Arr::get($tag, 'name'), '_'),
                'name' => Arr::get($tag, 'name'),
                'description' => Arr::get($tag, 'description'),
                'type' => Arr::get($tag, 'type'),
                'example' => Arr::get($tag, 'example'),
                'output_constraint' => Arr::get($tag, 'outputConstraint'),
                'possible_values' => Arr::get($tag, 'possibleValues'),
                'significance' => Arr::get($tag, 'significance'),
            ]);
        }
    }
}
