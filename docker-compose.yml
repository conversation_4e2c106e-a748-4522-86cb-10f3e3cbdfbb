services:
    nginx:
        image: 'nginx:latest'
        ports:
            - '${HTTP_PORT:-8000}:80'
            - '${SSL_PORT:-443}:443'
        environment:
            - SSL_PORT=${SSL_PORT:-443}
            - APP_SERVICE=${APP_SERVICE:-laravel.test}
            - SERVER_NAME=${SERVER_NAME:-localhost}
            - SSL_DOMAIN=${SSL_DOMAIN:-localhost}
            - SSL_ALT_NAME=${SSL_ALT_NAME:-DNS:localhost}
        volumes:
            - 'sail-nginx:/etc/nginx/certs'
            - './vendor/ryoluo/sail-ssl/nginx/templates:/etc/nginx/templates'
            - './vendor/ryoluo/sail-ssl/nginx/generate-ssl-cert.sh:/docker-entrypoint.d/99-generate-ssl-cert.sh'
        depends_on:
            - ${APP_SERVICE:-laravel.test}
        networks:
            - sail
    laravel.test:
        build:
            context: './docker/8.4'
            dockerfile: Dockerfile
            args:
                WWWGROUP: '${WWWGROUP}'
        image: 'sail-8.4/app'
        extra_hosts:
            - 'host.docker.internal:host-gateway'
        ports:
            - '${APP_PORT:-80}:80'
            - '${VITE_PORT:-5173}:${VITE_PORT:-5173}'
        environment:
            WWWUSER: '${WWWUSER}'
            LARAVEL_SAIL: 1
            XDEBUG_MODE: '${SAIL_XDEBUG_MODE:-off}'
            XDEBUG_CONFIG: '${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}'
            IGNITION_LOCAL_SITES_PATH: '${PWD}'
        volumes:
            - '.:/var/www/html'
        networks:
            - sail
        depends_on:
            - pgsql
            - minio
            - mailpit
            - valkey
    pgsql:
        image: 'postgres:17'
        ports:
            - '${FORWARD_DB_PORT:-5432}:5432'
        environment:
            PGPASSWORD: '${DB_PASSWORD:-secret}'
            POSTGRES_DB: '${DB_DATABASE}'
            POSTGRES_USER: '${DB_USERNAME}'
            POSTGRES_PASSWORD: '${DB_PASSWORD:-secret}'
        volumes:
            - 'sail-pgsql:/var/lib/postgresql/data'
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - pg_isready
                - '-q'
                - '-d'
                - '${DB_DATABASE}'
                - '-U'
                - '${DB_USERNAME}'
            retries: 3
            timeout: 5s
    minio:
        image: 'minio/minio:latest'
        ports:
            - '${FORWARD_MINIO_PORT:-9000}:9000'
            - '${FORWARD_MINIO_CONSOLE_PORT:-8900}:8900'
        environment:
            MINIO_ROOT_USER: sail
            MINIO_ROOT_PASSWORD: password
        volumes:
            - 'sail-minio:/data'
        networks:
            - sail
        command: 'minio server /data --console-address ":8900"'
        healthcheck:
            test:
                - CMD
                - mc
                - ready
                - local
            retries: 3
            timeout: 5s
    mailpit:
        image: 'axllent/mailpit:latest'
        ports:
            - '${FORWARD_MAILPIT_PORT:-1025}:1025'
            - '${FORWARD_MAILPIT_DASHBOARD_PORT:-8025}:8025'
        networks:
            - sail
    valkey:
        image: 'valkey/valkey:alpine'
        ports:
            - '${FORWARD_VALKEY_PORT:-6379}:6379'
        volumes:
            - 'sail-valkey:/data'
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - valkey-cli
                - ping
            retries: 3
            timeout: 5s
networks:
    sail:
        driver: bridge
volumes:
    sail-nginx:
        driver: local
    sail-pgsql:
        driver: local
    sail-minio:
        driver: local
    sail-valkey:
        driver: local
