import { defineConfig } from 'vitepress'

// https://vitepress.dev/reference/site-config
export default defineConfig({
  title: "Ollie Documentation",
  description: "Documentation for the Ollie application",

  // Base path for <PERSON><PERSON> integration
  base: '/docs/',

  // Build directly to Laravel's public directory
  outDir: '../public/docs',

  // Development server configuration
  vite: {
    server: {
      port: 8081
    }
  },


  themeConfig: {
    // https://vitepress.dev/reference/default-theme-config
    nav: [
      { text: 'Overview', link: '/' },
      { text: 'Getting Started', link: '/getting-started' },
      { text: 'Domains', link: '/domains/' },
      { text: 'API', link: '/api/' }
    ],

    sidebar: [
      {
        text: 'Introduction',
        items: [
          { text: 'Application Overview', link: '/' },
          { text: 'Getting Started', link: '/getting-started' },
          { text: 'Installation', link: '/installation' }
        ]
      },
      {
        text: 'Domains',
        items: [
          { text: 'Overview', link: '/domains/' },
          {
            text: 'Global Domain',
            collapsed: true,
            items: [
              { text: 'Overview', link: '/domains/global/' },
              { text: 'Entities', link: '/domains/global/entities' },
              { text: 'API Reference', link: '/domains/global/api' }
            ]
          },
          {
            text: 'Leads Domain',
            collapsed: true,
            items: [
              { text: 'Overview', link: '/domains/leads/' },
              { text: 'Entities', link: '/domains/leads/entities' },
              { text: 'API Reference', link: '/domains/leads/api' }
            ]
          },
          {
            text: 'Radar Domain',
            collapsed: true,
            items: [
              { text: 'Overview', link: '/domains/radar/' },
              { text: 'Entities', link: '/domains/radar/entities' },
              { text: 'API Reference', link: '/domains/radar/api' }
            ]
          },
          {
            text: 'Suitability Reports',
            collapsed: true,
            items: [
              { text: 'Overview', link: '/domains/suitability-reports/' },
              { text: 'Entities', link: '/domains/suitability-reports/entities' },
              { text: 'API Reference', link: '/domains/suitability-reports/api' }
            ]
          }
        ]
      },
      {
        text: 'API Reference',
        items: [
          { text: 'Overview', link: '/api/' },
          { text: 'Customers', link: '/api/customers' },
          { text: 'Authentication', link: '/api/authentication' }
        ]
      }
    ],

    socialLinks: [
      { icon: 'github', link: 'https://github.com/your-org/ollie' }
    ],

    footer: {
      message: 'Built with VitePress',
      copyright: 'Copyright © 2024 Ollie'
    }
  }
})
