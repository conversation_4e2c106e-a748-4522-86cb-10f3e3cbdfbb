# Ollie Documentation

This directory contains the VitePress documentation for the Ollie application.

## Development

To run the documentation site locally:

```bash
npm run docs:dev
```

The documentation will be available at `http://localhost:5173`

## Building

To build the documentation for production:

```bash
npm run docs:build
```

The built files will be in `docs/.vitepress/dist`

## Preview

To preview the built documentation:

```bash
npm run docs:preview
```

## Structure

- `index.md` - Homepage
- `getting-started.md` - Getting started guide
- `installation.md` - Installation instructions
- `api/` - API documentation
  - `index.md` - API overview
  - `customers.md` - Customer API endpoints
  - `authentication.md` - Authentication API
- `.vitepress/config.js` - VitePress configuration

## Contributing

When adding new documentation:

1. Create markdown files in the appropriate directory
2. Update the sidebar configuration in `.vitepress/config.js`
3. Follow the existing documentation style and structure
4. Test locally with `npm run docs:dev`
