# Authentication API

The Authentication API handles user login, logout, and token management.

## Authentication Methods

The API supports the following authentication methods:

1. **Bearer Token Authentication** (recommended)
2. **Session-based Authentication**

## Endpoints

### Login

```http
POST /api/auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "your-password"
}
```

**Response:**
```json
{
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_in": 3600,
    "user": {
      "id": 1,
      "name": "<PERSON>",
      "email": "<EMAIL>"
    }
  },
  "message": "Login successful"
}
```

### Logout

```http
POST /api/auth/logout
```

**Headers:**
```http
Authorization: Bearer your-token-here
```

**Response:**
```json
{
  "message": "Successfully logged out"
}
```

### Refresh Token

```http
POST /api/auth/refresh
```

**Headers:**
```http
Authorization: Bearer your-token-here
```

**Response:**
```json
{
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_in": 3600
  },
  "message": "Token refreshed successfully"
}
```

### Get User Profile

```http
GET /api/auth/user
```

**Headers:**
```http
Authorization: Bearer your-token-here
```

**Response:**
```json
{
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "email_verified_at": "2024-01-01T12:00:00Z",
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

## Using Bearer Tokens

Include the token in the Authorization header for all authenticated requests:

```http
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## Token Expiration

- **Access tokens** expire after 1 hour
- **Refresh tokens** expire after 30 days
- Use the refresh endpoint to get new tokens before expiration

## Error Codes

- `INVALID_CREDENTIALS`: Email or password is incorrect
- `TOKEN_EXPIRED`: The provided token has expired
- `TOKEN_INVALID`: The provided token is invalid or malformed
- `UNAUTHORIZED`: Authentication required for this endpoint
- `ACCOUNT_LOCKED`: User account has been temporarily locked

## Security Best Practices

1. **Store tokens securely** - Never store tokens in localStorage in production
2. **Use HTTPS** - Always use HTTPS in production environments
3. **Token rotation** - Regularly refresh tokens before expiration
4. **Logout properly** - Always call the logout endpoint when users sign out
