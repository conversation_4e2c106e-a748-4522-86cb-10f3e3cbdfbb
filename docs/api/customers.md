# Customer API

The Customer API allows you to manage customer records with unique reference numbers.

## Customer Object

```json
{
  "reference": "OL-ABC-DEF-GHI",
  "first_name": "<PERSON>",
  "middle_names": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "date_of_birth": "1990-01-15",
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

## Endpoints

### List Customers

```http
GET /api/customers
```

**Parameters:**
- `page` (optional): Page number for pagination
- `per_page` (optional): Number of items per page (max 100)

**Response:**
```json
{
  "data": [
    {
      "reference": "OL-ABC-DEF-GHI",
      "first_name": "<PERSON>",
      "middle_names": "<PERSON>",
      "last_name": "<PERSON><PERSON>",
      "email": "<EMAIL>",
      "date_of_birth": "1990-01-15"
    }
  ],
  "meta": {
    "current_page": 1,
    "total": 150,
    "per_page": 20
  }
}
```

### Get Customer

```http
GET /api/customers/{reference}
```

**Parameters:**
- `reference`: Customer reference number (e.g., "OL-ABC-DEF-GHI")

**Response:**
```json
{
  "data": {
    "reference": "OL-ABC-DEF-GHI",
    "first_name": "John",
    "middle_names": "Michael",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "date_of_birth": "1990-01-15"
  }
}
```

### Create Customer

```http
POST /api/customers
```

**Request Body:**
```json
{
  "first_name": "John",
  "middle_names": "Michael",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "date_of_birth": "1990-01-15"
}
```

**Response:**
```json
{
  "data": {
    "reference": "OL-ABC-DEF-GHI",
    "first_name": "John",
    "middle_names": "Michael",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "date_of_birth": "1990-01-15"
  },
  "message": "Customer created successfully"
}
```

### Delete Customer

```http
DELETE /api/customers/{reference}
```

**Parameters:**
- `reference`: Customer reference number

**Response:**
```json
{
  "message": "Customer deleted successfully"
}
```

## Customer Reference Numbers

Customer reference numbers follow the format `OL-XXX-XXX-XXX` where:

- `OL` is the fixed prefix
- `XXX` represents segments of non-ambiguous alphanumeric characters
- The system starts with 3 segments and expands to 4 when needed
- Characters used: A-Z, 0-9 (excluding ambiguous characters like O, 0, I, 1)

## Search and Matching

When creating customers, the system performs exact matching on all provided fields:

- All name fields must match exactly (including middle names if present)
- Email must match exactly
- Date of birth must match exactly

Only when no exact match exists will a new customer record be created.

## Error Codes

- `CUSTOMER_NOT_FOUND`: Customer with specified reference not found
- `VALIDATION_ERROR`: Request data validation failed
- `DUPLICATE_CUSTOMER`: Customer already exists with provided details
