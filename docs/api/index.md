# API Reference

The Ollie API provides RESTful endpoints for managing customers and related data.

## Base URL

```
https://your-domain.com/api
```

## Authentication

The API uses standard authentication mechanisms. Include your authentication token in the request headers:

```http
Authorization: Bearer your-token-here
```

## Response Format

All API responses follow a consistent JSON format:

```json
{
  "data": {
    // Response data here
  },
  "message": "Success message",
  "status": "success"
}
```

## Error Handling

Error responses include appropriate HTTP status codes and error details:

```json
{
  "error": {
    "message": "Error description",
    "code": "ERROR_CODE"
  },
  "status": "error"
}
```

## Rate Limiting

API requests are rate-limited to ensure fair usage:

- **Authenticated requests**: 1000 requests per hour
- **Unauthenticated requests**: 100 requests per hour

## Available Endpoints

### Customers

- [Customer Management](./customers.md) - Create, read, and manage customer records

### Authentication

- [Authentication](./authentication.md) - Login, logout, and token management

## SDKs and Libraries

Official SDKs are available for:

- JavaScript/Node.js
- PHP
- Python

## Postman Collection

Download our [Postman collection](./postman-collection.json) for easy API testing.

## Support

For API support and questions:

- Email: <EMAIL>
- Documentation: This site
- Status Page: https://status.ollie.com
