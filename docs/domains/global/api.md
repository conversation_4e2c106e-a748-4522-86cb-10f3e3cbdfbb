# Global Domain - API Reference

The Global domain primarily provides internal services and utilities rather than public API endpoints. However, it establishes patterns and shared functionality used by other domain APIs.

## Service APIs

### HubSpotApiService

The HubSpotApiService provides methods for interacting with the HubSpot CRM API.

#### getContactById

Retrieves a contact from HubSpot by ID.

**Method Signature:**
```php
public function getContactById(string $contactId): Error|SimplePublicObjectWithAssociations
```

**Parameters:**
- `$contactId` (string) - The HubSpot contact ID

**Returns:**
- `SimplePublicObjectWithAssociations` - Contact data on success
- `Error` - Error object on failure

**Throws:**
- `HubSpot\Client\Crm\Contacts\ApiException` - On API errors

**Example Usage:**
```php
use App\Domains\Global\Hubspot\Services\HubSpotApiService;

$hubSpotService = app(HubSpotApiService::class);

try {
    $contact = $hubSpotService->getContactById('12345');
    $properties = $contact->getProperties();
    
    $firstName = $properties['firstname'] ?? '';
    $lastName = $properties['lastname'] ?? '';
    $email = $properties['email'] ?? '';
    
} catch (ApiException $e) {
    Log::error("Failed to fetch contact: {$e->getMessage()}");
}
```

**Response Structure:**
```php
// Contact properties array
[
    'firstname' => 'John',
    'lastname' => 'Doe', 
    'email' => '<EMAIL>',
    'phone' => '+1234567890',
    'company' => 'Example Corp',
    'createdate' => '2024-01-01T12:00:00.000Z',
    'lastmodifieddate' => '2024-01-15T14:30:00.000Z',
    // ... other HubSpot properties
]
```

## Utility APIs

### EnumToArray Trait

Provides utility methods for PHP enumerations.

#### array()

Returns an associative array with enum values as keys and names as values.

**Method Signature:**
```php
public static function array(): array
```

**Example:**
```php
enum Status: string
{
    use EnumToArray;
    
    case ACTIVE = 'active';
    case INACTIVE = 'inactive';
    case PENDING = 'pending';
}

$options = Status::array();
// Result: ['active' => 'ACTIVE', 'inactive' => 'INACTIVE', 'pending' => 'PENDING']
```

#### values()

Returns an array of all enum values.

**Method Signature:**
```php
public static function values(): array
```

**Example:**
```php
$values = Status::values();
// Result: ['active', 'inactive', 'pending']
```

#### names()

Returns an array of all enum names.

**Method Signature:**
```php
public static function names(): array
```

**Example:**
```php
$names = Status::names();
// Result: ['ACTIVE', 'INACTIVE', 'PENDING']
```

## Configuration APIs

### Service Configuration Access

The Global domain provides standardized access to external service configurations.

#### HubSpot Configuration
```php
// Access HubSpot configuration
$portalId = config('services.hubspot.portal_id');
$apiKey = config('services.hubspot.api_key');
$webhookSecret = config('services.hubspot.webhook_secret');
```

#### OpenAI Configuration
```php
// Access OpenAI configuration
$apiKey = config('services.openai.api_key');
$organization = config('services.openai.organization');
```

## Error Handling Patterns

### Exception Handling

The Global domain establishes consistent error handling patterns:

```php
try {
    $result = $externalService->performOperation();
} catch (ExternalServiceException $e) {
    Log::error('External service error', [
        'service' => $e->getService(),
        'operation' => $e->getOperation(),
        'message' => $e->getMessage(),
        'context' => $e->getContext()
    ]);
    
    // Handle graceful degradation
    return $this->handleServiceFailure($e);
}
```

### Logging Patterns

Structured logging with consistent context:

```php
Log::info('HubSpot API call', [
    'operation' => 'getContactById',
    'contact_id' => $contactId,
    'response_time' => $responseTime,
    'success' => true
]);

Log::error('HubSpot API error', [
    'operation' => 'getContactById',
    'contact_id' => $contactId,
    'error_code' => $e->getCode(),
    'error_message' => $e->getMessage(),
    'trace_id' => $traceId
]);
```

## Caching Patterns

### Cache Key Generation

Standardized cache key patterns:

```php
class CacheKeys
{
    public const HUBSPOT_CONTACT = 'hubspot:contact:%s';
    public const LEAD_SOURCES = 'lead_sources:active';
    
    public static function hubspotContact(string $contactId): string
    {
        return sprintf(self::HUBSPOT_CONTACT, $contactId);
    }
}

// Usage
$cacheKey = CacheKeys::hubspotContact($contactId);
$contact = Cache::remember($cacheKey, 3600, function () use ($contactId) {
    return $this->hubSpotService->getContactById($contactId);
});
```

## Validation Patterns

### Input Validation

Common validation patterns used across domains:

```php
class ValidationRules
{
    public static function hubspotId(): array
    {
        return ['required', 'string', 'regex:/^\d+$/'];
    }
    
    public static function email(): array
    {
        return ['required', 'email', 'max:255'];
    }
    
    public static function phoneNumber(): array
    {
        return ['nullable', 'string', 'regex:/^[\+]?[0-9\s\-\(\)]+$/'];
    }
}
```

## Response Formatting

### Standardized API Responses

The Global domain establishes response formatting patterns:

```php
class ApiResponse
{
    public static function success($data, string $message = 'Success'): array
    {
        return [
            'status' => 'success',
            'message' => $message,
            'data' => $data,
            'timestamp' => now()->toISOString()
        ];
    }
    
    public static function error(string $message, int $code = 400, array $errors = []): array
    {
        return [
            'status' => 'error',
            'message' => $message,
            'code' => $code,
            'errors' => $errors,
            'timestamp' => now()->toISOString()
        ];
    }
}
```

## Integration Examples

### Using Global Services in Other Domains

#### Leads Domain Integration
```php
// In Leads domain service
class DealService
{
    public function __construct(
        private HubSpotApiService $hubSpotService
    ) {}
    
    public function syncDealFromHubSpot(string $dealId): Deal
    {
        $hubspotDeal = $this->hubSpotService->getDealById($dealId);
        
        return Deal::updateOrCreate(
            ['deal_object_id' => $dealId],
            [
                'deal_name' => $hubspotDeal->getProperty('dealname'),
                'deal_stage' => $hubspotDeal->getProperty('dealstage'),
                // ... other properties
            ]
        );
    }
}
```

#### Suitability Reports Integration
```php
// In SuitabilityReports domain controller
class StartSuitabilityReportFromHubspotController
{
    public function __invoke(
        Request $request,
        HubSpotApiService $hubSpotApiService,
        string $hubspotId
    ) {
        $contact = $hubSpotApiService->getContactById($hubspotId);
        $properties = $contact->getProperties();
        
        $customer = SuitabilityReportCustomer::create([
            'hubspot_id' => $hubspotId,
            'name' => trim($properties['firstname'] . ' ' . $properties['lastname']),
            'estate_planner_id' => $request->user()->id,
        ]);
        
        return redirect()->route('suitability.show', $customer);
    }
}
```

## Testing APIs

### Mock Service Implementations

For testing, the Global domain provides mock implementations:

```php
class MockHubSpotApiService implements HubSpotApiServiceInterface
{
    public function getContactById(string $contactId): array
    {
        return [
            'id' => $contactId,
            'properties' => [
                'firstname' => 'Test',
                'lastname' => 'User',
                'email' => '<EMAIL>',
                'phone' => '+1234567890'
            ]
        ];
    }
}
```

### Test Helpers

```php
class GlobalTestHelper
{
    public static function mockHubSpotContact(array $overrides = []): array
    {
        return array_merge([
            'id' => '12345',
            'properties' => [
                'firstname' => 'John',
                'lastname' => 'Doe',
                'email' => '<EMAIL>'
            ]
        ], $overrides);
    }
}
```

## Best Practices

### Service Usage
- Always inject services through dependency injection
- Use try-catch blocks for external API calls
- Implement proper logging for debugging
- Cache expensive operations appropriately

### Error Handling
- Use specific exception types
- Provide meaningful error messages
- Include relevant context in logs
- Implement graceful degradation

### Configuration
- Use environment variables for sensitive data
- Validate configuration on application boot
- Provide sensible defaults where possible
- Document all configuration options
