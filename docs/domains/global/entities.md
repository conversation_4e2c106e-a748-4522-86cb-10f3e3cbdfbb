# Global Domain - Entities

The Global domain primarily consists of services and utilities rather than traditional entities. However, it provides foundational components that other domains build upon.

## Core Components

### Services

#### HubSpotApiService
**Location**: `App\Domains\Global\Hubspot\Services\HubSpotApiService`

The central service for all HubSpot API interactions across the application.

**Key Properties:**
- `$hubSpot` - HubSpot API client instance
- Comprehensive error handling and logging
- Automatic retry logic for API failures

**Key Methods:**
```php
public function getContactById(string $contactId): Error|SimplePublicObjectWithAssociations
```

**Responsibilities:**
- Contact retrieval and management
- API error handling and logging
- Connection management
- Response transformation

**Usage Across Domains:**
- **Leads Domain**: Synchronizing deal and contact information
- **Suitability Reports**: Fetching customer data from HubSpot
- **General**: Any HubSpot integration needs

### Traits

#### EnumToArray
**Location**: `App\Domains\Global\Enums\EnumToArray`

A utility trait that provides array conversion methods for PHP enumerations.

**Methods:**
```php
public static function array(): array
public static function values(): array  
public static function names(): array
```

**Implementation:**
```php
trait EnumToArray
{
    public static function array(): array
    {
        return array_combine(self::values(), self::names());
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function names(): array
    {
        return array_column(self::cases(), 'name');
    }
}
```

**Usage Pattern:**
```php
enum WebhookEventType: string
{
    use EnumToArray;
    
    case DEAL_CREATED = 'deal.created';
    case DEAL_UPDATED = 'deal.updated';
}

// Get all values: ['deal.created', 'deal.updated']
$values = WebhookEventType::values();

// Get combined array: ['deal.created' => 'DEAL_CREATED', ...]
$options = WebhookEventType::array();
```

## Configuration Entities

### Service Configuration
The Global domain manages configuration for external services:

#### HubSpot Configuration
```php
// config/services.php
'hubspot' => [
    'api_key' => env('HUBSPOT_API_KEY'),
    'portal_id' => env('HUBSPOT_PORTAL_ID'),
    'webhook_secret' => env('HUBSPOT_WEBHOOK_SECRET'),
]
```

#### OpenAI Configuration
```php
'openai' => [
    'api_key' => env('OPENAI_API_KEY'),
    'organization' => env('OPENAI_ORGANIZATION'),
]
```

## Data Transfer Objects (DTOs)

While the Global domain doesn't define its own DTOs, it provides patterns and base classes that other domains extend.

### Common DTO Patterns
- Readonly classes for immutability
- Constructor property promotion
- Type-safe property definitions
- Validation at construction time

## Shared Interfaces

### Service Contracts
The Global domain establishes contracts that other domains implement:

```php
interface ExternalApiServiceInterface
{
    public function authenticate(): bool;
    public function handleError(Exception $exception): void;
    public function retry(callable $operation, int $maxAttempts = 3): mixed;
}
```

## Error Handling Entities

### Exception Types
The Global domain defines common exception patterns:

```php
class ExternalServiceException extends Exception
{
    public function __construct(
        string $service,
        string $operation,
        string $message,
        int $code = 0,
        ?Throwable $previous = null
    ) {
        parent::__construct(
            "External service error in {$service}::{$operation}: {$message}",
            $code,
            $previous
        );
    }
}
```

## Utility Classes

### Data Transformers
Common data transformation utilities:

```php
class DataTransformer
{
    public static function normalizePhoneNumber(string $phone): string
    public static function formatCurrency(float $amount, string $currency = 'GBP'): string
    public static function sanitizeEmail(string $email): string
}
```

### Validation Helpers
Shared validation logic:

```php
class ValidationHelper
{
    public static function isValidEmail(string $email): bool
    public static function isValidPhoneNumber(string $phone): bool
    public static function isValidPostcode(string $postcode): bool
}
```

## Caching Entities

### Cache Keys
Standardized cache key patterns:

```php
class CacheKeys
{
    public const HUBSPOT_CONTACT = 'hubspot:contact:%s';
    public const LEAD_SOURCES = 'lead_sources:active';
    public const DEAL_STAGES = 'deal_stages:all';
    
    public static function hubspotContact(string $contactId): string
    {
        return sprintf(self::HUBSPOT_CONTACT, $contactId);
    }
}
```

## Logging Entities

### Log Contexts
Structured logging contexts:

```php
class LogContext
{
    public static function hubspotApi(string $operation, array $data = []): array
    {
        return [
            'service' => 'hubspot',
            'operation' => $operation,
            'timestamp' => now()->toISOString(),
            ...$data
        ];
    }
}
```

## Relationships with Other Domains

### Dependency Flow
```mermaid
graph TB
    subgraph "Global Domain"
        HubSpot[HubSpotApiService]
        Enums[EnumToArray Trait]
        Utils[Utility Classes]
    end
    
    subgraph "Leads Domain"
        Deal[Deal Model]
        LeadService[Lead Services]
    end
    
    subgraph "Suitability Domain"
        Customer[Customer Model]
        SuitService[Suitability Services]
    end
    
    HubSpot --> LeadService
    HubSpot --> SuitService
    Enums --> Deal
    Utils --> LeadService
    Utils --> SuitService
```

## Best Practices

### Service Design
- Single responsibility principle
- Dependency injection for testability
- Comprehensive error handling
- Consistent logging patterns

### Configuration Management
- Environment-specific settings
- Validation of required configuration
- Secure credential handling
- Default value management

### Error Handling
- Structured exception hierarchy
- Contextual error information
- Graceful degradation
- Comprehensive logging

## Testing Considerations

### Mock Services
The Global domain provides mock implementations for testing:

```php
class MockHubSpotApiService extends HubSpotApiService
{
    public function getContactById(string $contactId): array
    {
        return [
            'id' => $contactId,
            'properties' => [
                'firstname' => 'Test',
                'lastname' => 'User',
                'email' => '<EMAIL>'
            ]
        ];
    }
}
```

### Test Utilities
Common testing utilities and helpers:

```php
class TestHelper
{
    public static function createMockHubSpotContact(array $overrides = []): array
    public static function mockExternalApiResponse(string $service, array $data): void
}
```
