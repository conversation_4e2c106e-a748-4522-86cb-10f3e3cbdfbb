# Global Domain

The Global domain provides shared services, utilities, and integrations used across the entire Ollie application. It serves as the foundation layer that other domains depend on for common functionality.

## Purpose

The Global domain encapsulates:
- **External Service Integrations**: HubSpot API, OpenAI, and other third-party services
- **Shared Utilities**: Common enumerations, traits, and helper functions
- **Cross-cutting Concerns**: Logging, caching, and configuration management
- **Foundation Services**: Base classes and interfaces used by other domains

## Key Components

### HubSpot Integration
Centralized service for all HubSpot API interactions:
- Contact management and retrieval
- Deal synchronization
- Webhook processing
- Error handling and logging

### Shared Enumerations
Common enums with utility methods:
- `EnumToArray` trait for array conversion
- Consistent value and name extraction
- Type-safe enumeration handling

### Utilities & Helpers
- Configuration management
- Data transformation utilities
- Validation helpers
- Common patterns and abstractions

## Architecture

The Global domain follows a service-oriented architecture:

```mermaid
graph TB
    subgraph "Global Domain"
        HubSpot[HubSpot API Service]
        Enums[Shared Enumerations]
        Utils[Utilities & Helpers]
        Config[Configuration]
    end
    
    subgraph "Other Domains"
        Leads[Leads Domain]
        Radar[Radar Domain]
        Suitability[Suitability Reports]
    end
    
    HubSpot --> Leads
    HubSpot --> Suitability
    Enums --> Leads
    Enums --> Radar
    Utils --> Leads
    Utils --> Radar
    Utils --> Suitability
```

## Services

### HubSpotApiService
The primary service for HubSpot integration:

**Key Methods:**
- `getContactById(string $contactId)` - Retrieve contact information
- Error handling with comprehensive logging
- Automatic retry logic for transient failures

**Usage Pattern:**
```php
$hubSpotService = app(HubSpotApiService::class);
$contact = $hubSpotService->getContactById($hubspotId);
```

## Shared Enumerations

### EnumToArray Trait
Provides utility methods for enum handling:

**Available Methods:**
- `array()` - Get combined values and names
- `values()` - Get all enum values
- `names()` - Get all enum names

**Example Usage:**
```php
enum Status: string
{
    use EnumToArray;
    
    case ACTIVE = 'active';
    case INACTIVE = 'inactive';
}

// Usage
$options = Status::array(); // ['active' => 'ACTIVE', 'inactive' => 'INACTIVE']
$values = Status::values(); // ['active', 'inactive']
```

## Integration Patterns

### Service Injection
Global services are injected into other domain services:

```php
class SomeService
{
    public function __construct(
        private HubSpotApiService $hubSpotService
    ) {}
}
```

### Configuration Access
Centralized configuration management:

```php
$portalId = config('services.hubspot.portal_id');
$apiKey = config('services.hubspot.api_key');
```

## Error Handling

The Global domain implements consistent error handling:
- Structured logging with context
- Exception wrapping and re-throwing
- Graceful degradation for external services
- Comprehensive error reporting

## Best Practices

### Service Design
- Single responsibility principle
- Dependency injection for testability
- Interface-based contracts
- Comprehensive error handling

### Configuration
- Environment-specific settings
- Secure credential management
- Validation of required configuration
- Default value handling

### Logging
- Structured logging with context
- Appropriate log levels
- Performance monitoring
- Error tracking integration

## Dependencies

### External Services
- **HubSpot CRM API**: Customer and deal management
- **Sentry**: Error tracking and monitoring
- **Laravel Framework**: Core application services

### Internal Dependencies
- Configuration system
- Logging infrastructure
- Cache management
- Queue system

## Testing

The Global domain includes comprehensive tests:
- Unit tests for service methods
- Integration tests for external APIs
- Mock implementations for testing
- Error scenario coverage

## Next Steps

- [Entities Documentation](./entities.md) - Detailed model information
- [API Reference](./api.md) - Available endpoints and methods
- [Leads Domain](../leads/) - Lead management functionality
- [Radar Domain](../radar/) - Document analysis features
