# Domain Architecture

The Ollie application is organized using Domain-Driven Design (DDD) principles, with business logic separated into distinct domains. Each domain encapsulates related functionality, models, and services.

## Domain Overview

### [Global Domain](./global/)
**Shared Services & Utilities**

The Global domain contains shared services, utilities, and integrations used across the entire application.

- **HubSpot Integration**: Centralized HubSpot API service
- **Shared Enums**: Common enumerations and utilities
- **Cross-domain Services**: Utilities used by multiple domains

### [Leads Domain](./leads/)
**Lead Management & Deal Tracking**

The Leads domain manages the lead lifecycle, deal tracking, and integration with HubSpot for sales pipeline management.

- **Deal Management**: Track deals through various stages
- **Lead Sources**: Manage and configure lead sources with SLA tracking
- **HubSpot Webhooks**: Real-time synchronization with HubSpot
- **SLA Calculations**: Automated call scheduling and breach detection

### [Radar Domain](./radar/)
**Document Analysis & Scoring**

The Radar domain provides intelligent document analysis, particularly for legal documents like wills, with AI-powered insights and scoring.

- **Document Processing**: OCR text extraction and analysis
- **AI Insights**: OpenAI-powered document analysis
- **Scoring System**: Rule-based scoring for document quality
- **Tag Extraction**: Automated tagging and categorization

### [Suitability Reports Domain](./suitability-reports/)
**Customer Assessment & Recommendations**

The Suitability Reports domain manages customer suitability assessments for legal services, generating personalized recommendations.

- **Customer Profiles**: Detailed customer information and preferences
- **Suitability Assessment**: Comprehensive questionnaire system
- **Recommendations**: AI-generated service recommendations
- **HubSpot Integration**: Seamless customer data synchronization

## Domain Relationships

```mermaid
graph TB
    Global[Global Domain<br/>Shared Services]
    Leads[Leads Domain<br/>Deal Management]
    Radar[Radar Domain<br/>Document Analysis]
    Suitability[Suitability Reports<br/>Customer Assessment]
    
    Global --> Leads
    Global --> Radar
    Global --> Suitability
    
    Leads -.-> Suitability
    Radar -.-> Suitability
```

## Key Design Principles

### Domain Isolation
Each domain maintains its own:
- Models and entities
- Business logic and services
- Data transfer objects (DTOs)
- Validation rules and forms

### Shared Dependencies
The Global domain provides:
- Common utilities and helpers
- External service integrations
- Shared enumerations
- Cross-cutting concerns

### Clear Boundaries
- Domains communicate through well-defined interfaces
- No direct model dependencies between domains
- Event-driven communication where appropriate
- Consistent API patterns across domains

## Getting Started

To understand a specific domain:

1. **Read the Overview** - Understand the domain's purpose and scope
2. **Study the Entities** - Learn about the models and relationships
3. **Explore the API** - Review available endpoints and operations
4. **Check Examples** - See practical usage patterns

Each domain section includes comprehensive documentation covering these areas.
