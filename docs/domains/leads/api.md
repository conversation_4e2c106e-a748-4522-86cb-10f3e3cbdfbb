# Leads Domain - API Reference

The Leads domain provides webhook endpoints for HubSpot integration and internal APIs for deal management.

## Webhook Endpoints

### HubSpot Webhook Handler

Receives and processes webhook events from HubSpot CRM.

```http
POST /api/hubspot/webhook
```

**Authentication**: HubSpot webhook signature verification

**Content-Type**: `application/json`

**Request Body:**
```json
{
  "event_type": "deal.created",
  "deal_id": 12345,
  "created_date": 1640995200,
  "created_by_id": 67890,
  "deal_name": "Example Deal",
  "deal_stage_id": 3,
  "ep_lead_gen": "Google Ads",
  "ep_lead_source": "PPC",
  "hs_priority": "high",
  "marketing_status": "qualified"
}
```

**Supported Event Types:**

#### deal.created
Triggered when a new deal is created in HubSpot.

**Required Fields:**
- `event_type`: "deal.created"
- `deal_id`: HubSpot deal ID (integer)
- `created_date`: Unix timestamp

**Optional Fields:**
- `created_by_id`: User ID who created the deal
- `deal_name`: Name/title of the deal
- `deal_stage_id`: Current stage ID
- `ep_lead_gen`: Lead generation method
- `ep_lead_source`: Source of the lead
- `hs_priority`: Deal priority level
- `marketing_status`: Marketing qualification status

#### deal.stage_changed
Triggered when a deal moves between stages.

**Required Fields:**
- `event_type`: "deal.stage_changed"
- `deal_id`: HubSpot deal ID (integer)
- `created_date`: Unix timestamp

**Optional Fields:**
- `updated_date`: When the change occurred
- `deal_stage_id`: New stage ID
- `deal_name`: Deal name
- `ep_lead_gen`: Lead generation method
- `ep_lead_source`: Lead source
- `hs_priority`: Priority level
- `marketing_status`: Marketing status

#### call.count
Triggered when call activity is updated.

**Required Fields:**
- `event_type`: "call.count"
- `deal_id`: HubSpot deal ID (integer)

**Optional Fields:**
- `call_count`: Number of calls made

**Response Format:**

**Success Response:**
```json
{
  "status": "success",
  "message": "Event processed successfully",
  "data": {
    "event_type": "deal.created",
    "deal_id": 12345,
    "processed_at": "2024-01-15T10:30:00Z"
  }
}
```

**Error Responses:**

**Validation Error (422):**
```json
{
  "error": "Validation failed",
  "details": {
    "event_type": ["The event_type field is required."],
    "deal_id": ["The deal_id must be an integer."]
  }
}
```

**Invalid Event Type (422):**
```json
{
  "error": "Invalid event type",
  "message": "Unsupported webhook event type"
}
```

**Processing Error (401):**
```json
{
  "error": "Failed to handle webhook",
  "message": "Internal processing error occurred"
}
```

## Internal APIs

### Deal Management

While primarily webhook-driven, the Leads domain provides internal APIs for deal management.

#### Get Deal by HubSpot ID

```php
// Service method
public function getDealByHubSpotId(string $dealObjectId): ?Deal
{
    return Deal::where('deal_object_id', $dealObjectId)->first();
}
```

#### Update Deal SLA

```php
// Service method
public function updateDealSla(Deal $deal, array $slaTimestamps): Deal
{
    $deal->update([
        'call_slas' => $slaTimestamps,
        'call_by' => $slaTimestamps[0] ?? null
    ]);
    
    return $deal->fresh();
}
```

#### Check SLA Breaches

```php
// Service method
public function getBreachedDeals(): Collection
{
    return Deal::query()
        ->whereNotNull('call_by')
        ->where('call_by', '<', now())
        ->with('dealStage')
        ->get();
}
```

## SLA Calculation API

### Calculate Next Call Time

```php
// SlaCalculationService method
public function calculateNextCallTimeForLeadSource(
    string $leadSource,
    Carbon|string $createdAt,
    bool $isFirstCall = true
): Carbon|null
```

**Parameters:**
- `$leadSource`: Name of the lead source
- `$createdAt`: When the lead was created
- `$isFirstCall`: Whether this is the first call (true) or subsequent call (false)

**Returns:**
- `Carbon`: Next call time in UTC
- `null`: If no more calls should be scheduled

**Business Rules:**
- Respects London business hours (9 AM - 5 PM, Mon-Fri)
- Adjusts for weekends and holidays
- Uses lead source specific SLA rules
- Calculates progressive call intervals

**Example Usage:**
```php
$slaService = app(SlaCalculationService::class);

// Calculate first call time
$firstCall = $slaService->calculateNextCallTimeForLeadSource(
    'Google Ads',
    '2024-01-15T08:00:00Z',
    true
);

// Calculate second call time
$secondCall = $slaService->calculateNextCallTimeForLeadSource(
    'Google Ads',
    '2024-01-15T08:00:00Z',
    false
);
```

## Event Processing Flow

### Webhook Processing Pipeline

```mermaid
sequenceDiagram
    participant H as HubSpot
    participant W as Webhook Controller
    participant S as HubSpot Event Service
    participant D as Deal Model
    participant Q as Queue System
    participant SLA as SLA Service

    H->>W: POST /api/hubspot/webhook
    W->>W: Validate request
    W->>S: Process event DTO
    
    alt Deal Created
        S->>D: Create/Update Deal
        D->>Q: Dispatch SLA calculation job
        Q->>SLA: Calculate call times
        SLA->>D: Update call_slas
    else Stage Changed
        S->>D: Update deal stage
        D->>Q: Dispatch update jobs
    else Call Count
        S->>D: Update call count
        D->>SLA: Recalculate next call
    end
    
    S->>W: Return response
    W->>H: JSON response
```

## Error Handling

### Webhook Error Scenarios

#### Invalid Signature
```json
{
  "error": "Invalid webhook signature",
  "code": 401
}
```

#### Malformed Payload
```json
{
  "error": "Invalid JSON payload",
  "code": 400
}
```

#### Unknown Event Type
```json
{
  "error": "Unsupported event type",
  "code": 422,
  "supported_types": [
    "deal.created",
    "deal.stage_changed", 
    "call.count"
  ]
}
```

#### Processing Failure
```json
{
  "error": "Event processing failed",
  "code": 500,
  "message": "Internal server error during event processing"
}
```

### Retry Logic

The system implements automatic retry logic for failed webhook processing:

1. **Immediate Retry**: First failure triggers immediate retry
2. **Exponential Backoff**: Subsequent failures use exponential backoff
3. **Dead Letter Queue**: After 3 failures, events go to dead letter queue
4. **Manual Recovery**: Failed events can be manually reprocessed

## Rate Limiting

### HubSpot Webhook Limits
- **Rate Limit**: 100 requests per second per portal
- **Burst Limit**: 1000 requests per minute
- **Daily Limit**: 1,000,000 requests per day

### Response Time SLA
- **Target**: < 200ms for webhook processing
- **Timeout**: 30 seconds maximum processing time
- **Async Processing**: Long-running tasks moved to background jobs

## Monitoring & Observability

### Webhook Metrics
- **Success Rate**: Percentage of successfully processed webhooks
- **Processing Time**: Average and P95 processing times
- **Error Rate**: Rate of webhook processing failures
- **Event Volume**: Number of events processed per time period

### SLA Metrics
- **Breach Rate**: Percentage of deals with SLA breaches
- **Average Response Time**: Time from lead creation to first call
- **Call Success Rate**: Percentage of successful call attempts
- **Source Performance**: SLA performance by lead source

### Logging

**Webhook Processing:**
```php
Log::info('HubSpot webhook received', [
    'event_type' => $eventType,
    'deal_id' => $dealId,
    'processing_time' => $processingTime
]);
```

**SLA Calculations:**
```php
Log::info('SLA calculated', [
    'deal_id' => $deal->id,
    'lead_source' => $deal->lead_source,
    'next_call' => $nextCall->toISOString(),
    'is_breach' => $deal->isSlaBreached()
]);
```

**Error Logging:**
```php
Log::error('Webhook processing failed', [
    'event_type' => $eventType,
    'deal_id' => $dealId,
    'error' => $exception->getMessage(),
    'trace' => $exception->getTraceAsString()
]);
```

## Testing

### Webhook Testing

**Test Payload:**
```json
{
  "event_type": "deal.created",
  "deal_id": 999999,
  "created_date": 1640995200,
  "deal_name": "Test Deal",
  "deal_stage_id": 1,
  "ep_lead_source": "Test Source"
}
```

**cURL Example:**
```bash
curl -X POST http://localhost:8000/api/hubspot/webhook \
  -H "Content-Type: application/json" \
  -H "X-HubSpot-Signature: sha256=..." \
  -d @test-webhook.json
```

### Mock Services

```php
// Test helper for mocking HubSpot webhooks
class HubSpotWebhookTestHelper
{
    public static function createDealCreatedPayload(array $overrides = []): array
    {
        return array_merge([
            'event_type' => 'deal.created',
            'deal_id' => 12345,
            'created_date' => time(),
            'deal_name' => 'Test Deal',
            'deal_stage_id' => 1
        ], $overrides);
    }
}
```

## Best Practices

### Webhook Handling
- Always validate webhook signatures
- Process webhooks asynchronously for better performance
- Implement idempotent processing to handle duplicates
- Log all webhook events for debugging and monitoring

### SLA Management
- Configure realistic SLA intervals based on business requirements
- Monitor SLA breach rates and adjust rules accordingly
- Consider business hours and holidays in calculations
- Provide clear escalation paths for breached SLAs

### Error Recovery
- Implement comprehensive error handling
- Use dead letter queues for failed events
- Provide manual recovery mechanisms
- Monitor error rates and investigate patterns
