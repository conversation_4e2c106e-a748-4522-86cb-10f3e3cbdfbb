# Leads Domain - Entities

The Leads domain contains several key entities that manage the lead lifecycle, deal tracking, and SLA management.

## Core Models

### Deal
**Location**: `App\Domains\Leads\Models\Deal`

The central entity representing a sales opportunity synchronized with HubSpot.

**Database Table**: `leads_deals`

**Key Properties:**
```php
protected $fillable = [
    'deal_object_id',      // HubSpot deal ID
    'portal_id',           // HubSpot portal ID
    'lead_source',         // Source of the lead
    'lead_gen',            // Lead generation method
    'marketing_status',    // Marketing campaign status
    'deal_name',           // Name/title of the deal
    'deal_stage',          // Current stage ID
    'deal_created_at',     // When deal was created in HubSpot
    'property_changed_at', // Last property change timestamp
    'call_slas',           // Array of scheduled call times
    'ranking',             // Priority ranking
    'call_count',          // Number of calls made
];
```

**Casts:**
```php
protected $casts = [
    'call_by' => 'datetime',
    'call_slas' => 'array',
    'deal_created_at' => 'datetime',
];
```

**Relationships:**
```php
// Belongs to a deal stage
public function dealStage(): BelongsTo
{
    return $this->belongsTo(DealStage::class, 'deal_stage', 'stage_id');
}
```

**Key Methods:**

#### getHubspotUrlAttribute()
Generates direct URL to HubSpot deal record:
```php
public function getHubspotUrlAttribute(): string
{
    return 'https://app.hubspot.com/contacts/' . 
           config('services.hubspot.portal_id') . 
           "/record/0-3/{$this->deal_object_id}";
}
```

#### nextCall()
Calculates the next scheduled call time:
```php
public function nextCall(): Carbon
{
    $slas = $this->call_slas ?? [];
    $time = $slas[$this->call_count] ?? null;
    return Carbon::parse($time);
}
```

#### isSlaBreached()
Determines if SLA has been breached:
```php
public function isSlaBreached(): bool
{
    $nextCallUtc = $this->nextCall();
    $timeNowInLondon = now('Europe/London')->shiftTimezone('UTC');
    return $timeNowInLondon->isAfter($nextCallUtc);
}
```

#### timeToNextCall()
Human-readable time until next call:
```php
public function timeToNextCall(): string
{
    $nextCallUtc = $this->nextCall();
    $timeNowInLondon = now('Europe/London')->shiftTimezone('UTC');
    
    return $nextCallUtc->diffForHumans($timeNowInLondon, [
        'syntax' => Carbon::DIFF_RELATIVE_TO_NOW,
        'parts' => 2,
    ]);
}
```

**Query Scopes:**
```php
// Only deals from active lead sources
#[Scope]
protected function activeSources(Builder $query): void
{
    $activeSources = Cache::remember('active-sources', 10, function () {
        return LeadSource::query()
            ->where('enabled', true)
            ->pluck('name');
    });
    
    $query->whereIn('lead_source', $activeSources);
}
```

**Observer**: `App\Domains\Leads\Observers\DealObserver`

### DealStage
**Location**: `App\Domains\Leads\Models\DealStage`

Represents different stages in the sales pipeline.

**Database Table**: `leads_deal_stages`

**Key Properties:**
```php
protected $fillable = [
    'stage_id',  // HubSpot stage identifier
    'label'      // Human-readable stage name
];
```

**Relationships:**
```php
// Has many deals in this stage
public function deals(): HasMany
{
    return $this->hasMany(Deal::class, 'deal_stage', 'stage_id');
}
```

**Usage:**
- Maps HubSpot stage IDs to readable labels
- Supports multiple sales pipelines
- Enables stage-based reporting and filtering

### LeadSource
**Location**: `App\Domains\Leads\Models\LeadSource`

Defines lead sources with specific SLA rules and configuration.

**Key Properties:**
```php
protected $fillable = [
    'name',         // Source name (e.g., "Google Ads")
    'slug',         // URL-friendly identifier
    'description',  // Detailed description
    'call_slas',    // Array of call timing intervals
    'max_calls',    // Maximum number of call attempts
    'ranking',      // Priority/importance score
];
```

**Casts:**
```php
protected $casts = [
    'call_slas' => 'array',
];
```

**SLA Configuration Example:**
```php
// call_slas array structure
[
    '2024-01-15T10:00:00.000Z',  // First call time
    '2024-01-15T14:00:00.000Z',  // Second call time
    '2024-01-16T09:00:00.000Z',  // Third call time
    // ... up to max_calls
]
```

**Usage:**
- Configures call timing rules per source
- Enables/disables sources dynamically
- Tracks source performance metrics
- Supports ranking for prioritization

## Data Transfer Objects (DTOs)

### HubSpot Webhook DTOs

#### DealCreatedDTO
**Location**: `App\Domains\Leads\DTO\WebhookEvents\DealCreatedDTO`

Represents a deal creation webhook event from HubSpot.

```php
final readonly class DealCreatedDTO extends HubSpotWebhookDTO
{
    public function __construct(
        public int $dealId,
        public int $createdAt,
        public ?int $createdById,
        public ?string $dealName,
        public ?int $dealStageId,
        public string|int $priority,
        public ?string $leadGen,
        public ?string $leadSource,
        public ?string $marketingStatus,
    ) {}
}
```

#### DealStageChangedDTO
**Location**: `App\Domains\Leads\DTO\WebhookEvents\DealStageChangedDTO`

Represents a deal stage change webhook event.

```php
final readonly class DealStageChangedDTO extends HubSpotWebhookDTO
{
    public function __construct(
        public int $dealId,
        public int $createdAt,
        public ?int $updatedAt,
        public ?int $dealStageId,
        public ?string $dealName,
        public ?string $leadGen,
        public ?string $leadSource,
        public string|int|null $priority,
        public ?string $marketingStatus,
    ) {}
}
```

#### CallCountDTO
**Location**: `App\Domains\Leads\DTO\WebhookEvents\CallCountDTO`

Represents a call count update webhook event.

```php
final readonly class CallCountDTO extends HubSpotWebhookDTO
{
    public function __construct(
        public int $dealId,
        public ?int $callCount,
    ) {}
}
```

## Enumerations

### WebhookEventType
**Location**: `App\Domains\Leads\Enum\WebhookEventType`

Defines supported HubSpot webhook event types.

```php
enum WebhookEventType: string
{
    use EnumToArray;
    
    case DEAL_CREATED = 'deal.created';
    case DEAL_STAGE_CHANGED = 'deal.stage_changed';
    case CALL_COUNT = 'call.count';
}
```

**Usage:**
- Validates incoming webhook events
- Routes events to appropriate handlers
- Provides type safety for event processing

## Services

### SlaCalculationService
**Location**: `App\Domains\Leads\Services\SlaCalculationService`

Handles all SLA-related calculations and business logic.

**Key Methods:**

#### calculateNextCallTimeForLeadSource()
```php
public function calculateNextCallTimeForLeadSource(
    string $leadSource,
    Carbon|string $createdAt,
    bool $isFirstCall = true
): Carbon|null
```

Calculates when the next call should be made based on:
- Lead source SLA rules
- Business hours (London timezone)
- Call sequence (first, second, etc.)
- Weekend and holiday considerations

#### isBusinessHour()
```php
private function isBusinessHour(Carbon $dateTime): bool
```

Determines if a given time falls within business hours:
- Monday-Friday: 9:00 AM - 5:00 PM London time
- Excludes weekends and holidays
- Used for SLA calculations

### HubSpotEventService
**Location**: `App\Domains\Leads\Hubspot\Services\HubSpotEventService`

Processes HubSpot webhook events and manages deal synchronization.

**Key Methods:**

#### processEvent()
```php
public function processEvent(
    DealCreatedDTO|DealStageChangedDTO|CallCountDTO $eventDTO
): JsonResponse
```

Routes webhook events to appropriate handlers:
- Deal creation events → `handleDealCreation()`
- Stage change events → `handlePropertyChange()`
- Call count events → `handleCallCreation()`

## Observers

### DealObserver
**Location**: `App\Domains\Leads\Observers\DealObserver`

Handles Deal model lifecycle events.

**Events:**
- `created()` - Triggers SLA calculation jobs
- `updated()` - Updates related caches
- `deleted()` - Cleanup related data

## Database Relationships

```mermaid
erDiagram
    DEAL {
        id bigint PK
        deal_object_id string UK
        portal_id string
        lead_source string FK
        deal_stage string FK
        deal_name string
        call_slas json
        call_count integer
        created_at timestamp
        updated_at timestamp
    }
    
    DEAL_STAGE {
        id bigint PK
        stage_id string UK
        label string
        created_at timestamp
        updated_at timestamp
    }
    
    LEAD_SOURCE {
        id bigint PK
        name string UK
        slug string
        call_slas json
        max_calls integer
        ranking integer
        enabled boolean
        created_at timestamp
        updated_at timestamp
    }
    
    DEAL ||--|| DEAL_STAGE : "belongs to"
    DEAL ||--|| LEAD_SOURCE : "sourced from"
```

## Validation Rules

### Deal Validation
```php
// Deal creation/update rules
[
    'deal_object_id' => 'required|string|unique:leads_deals',
    'portal_id' => 'required|string',
    'lead_source' => 'required|string|exists:lead_sources,name',
    'deal_stage' => 'nullable|string|exists:leads_deal_stages,stage_id',
    'deal_name' => 'nullable|string|max:255',
    'call_count' => 'integer|min:0',
]
```

### Webhook Validation
```php
// HubSpot webhook validation
[
    'event_type' => 'required|string|in:deal.created,deal.stage_changed,call.count',
    'deal_id' => 'required|integer',
    'created_date' => 'integer',
    'deal_name' => 'nullable|string',
    'deal_stage_id' => 'nullable|integer',
]
```

## Performance Considerations

### Indexing
Key database indexes:
- `deal_object_id` (unique)
- `lead_source` (for filtering)
- `deal_stage` (for stage queries)
- `call_by` (for SLA queries)

### Caching
Cached data:
- Active lead sources (10 minutes)
- Deal stages (1 hour)
- SLA calculations (5 minutes)

### Queue Processing
Background jobs for:
- SLA calculations
- HubSpot synchronization
- Breach notifications
- Performance metrics
