# Leads Domain

The Leads domain manages the complete lead lifecycle, from initial lead capture through deal progression and SLA management. It provides comprehensive lead tracking, deal management, and integration with HubSpot for sales pipeline automation.

## Purpose

The Leads domain handles:
- **Lead Management**: Capture, track, and manage leads from various sources
- **Deal Tracking**: Monitor deals through different stages of the sales pipeline
- **SLA Management**: Automated call scheduling and breach detection
- **HubSpot Integration**: Real-time synchronization with HubSpot CRM
- **Lead Source Management**: Configure and track performance of different lead sources

## Key Features

### Deal Management
- Track deals through customizable stages
- Monitor deal progression and history
- Calculate next call times based on SLA rules
- Detect and alert on SLA breaches
- Generate HubSpot URLs for easy access

### Lead Source Configuration
- Define lead sources with custom SLA rules
- Set maximum call attempts per source
- Configure call timing intervals
- Enable/disable sources dynamically
- Track source performance and rankings

### HubSpot Webhook Integration
- Real-time deal creation notifications
- Deal stage change tracking
- Call count updates
- Automatic data synchronization
- Error handling and retry logic

### SLA Calculation System
- Dynamic call scheduling based on lead source
- Business hours consideration (London timezone)
- Breach detection and alerting
- Configurable call intervals
- Support for multiple call attempts

## Architecture

```mermaid
graph TB
    subgraph "Leads Domain"
        subgraph "Models"
            Deal[Deal]
            DealStage[Deal Stage]
            LeadSource[Lead Source]
        end
        
        subgraph "Services"
            SLA[SLA Calculation Service]
            HubSpot[HubSpot Event Service]
        end
        
        subgraph "Controllers"
            Webhook[HubSpot Webhook Controller]
        end
        
        subgraph "Jobs & Events"
            Observer[Deal Observer]
            Jobs[Background Jobs]
        end
    end
    
    subgraph "External"
        HubSpotCRM[HubSpot CRM]
        Queue[Queue System]
    end
    
    HubSpotCRM --> Webhook
    Webhook --> HubSpot
    HubSpot --> Deal
    Deal --> Observer
    Observer --> Jobs
    Jobs --> Queue
    
    Deal --> DealStage
    Deal --> SLA
    SLA --> LeadSource
```

## Core Entities

### Deal
The central entity representing a sales opportunity:
- **HubSpot Integration**: Synced with HubSpot deal objects
- **Stage Tracking**: Current position in sales pipeline
- **SLA Management**: Automated call scheduling
- **Breach Detection**: Identifies overdue calls

### Deal Stage
Represents different stages in the sales pipeline:
- **Stage Configuration**: Customizable stage definitions
- **Stage Relationships**: Links deals to their current stage
- **Pipeline Management**: Supports multiple pipeline configurations

### Lead Source
Defines sources of leads with specific SLA rules:
- **SLA Configuration**: Custom call timing rules per source
- **Performance Tracking**: Rankings and success metrics
- **Source Management**: Enable/disable sources as needed

## Business Logic

### SLA Calculation
The domain implements sophisticated SLA calculation logic:

1. **Lead Source Rules**: Each source has specific call timing requirements
2. **Business Hours**: Calculations respect London business hours
3. **Call Sequences**: Multiple calls scheduled at increasing intervals
4. **Breach Detection**: Automatic identification of overdue calls

### Deal Lifecycle
Deals progress through a defined lifecycle:

1. **Creation**: New deals from HubSpot webhooks
2. **Stage Progression**: Movement through sales pipeline
3. **Call Scheduling**: Automatic SLA-based scheduling
4. **Completion**: Final stage reached or deal closed

## Integration Points

### HubSpot CRM
- **Webhook Events**: Real-time notifications for deal changes
- **Data Synchronization**: Bidirectional data sync
- **URL Generation**: Direct links to HubSpot records
- **Error Handling**: Robust error handling and logging

### Queue System
- **Background Processing**: Asynchronous webhook processing
- **Job Scheduling**: Delayed job execution for SLA calculations
- **Error Recovery**: Automatic retry logic for failed jobs

## Key Services

### SlaCalculationService
Handles all SLA-related calculations:
- Call time calculation based on lead source
- Business hours consideration
- Breach detection logic
- Support for multiple call attempts

### HubSpotEventService
Processes HubSpot webhook events:
- Event type routing and handling
- Data transformation and validation
- Deal creation and updates
- Error handling and logging

## API Endpoints

### HubSpot Webhook
```http
POST /api/hubspot/webhook
```

Receives webhook events from HubSpot for:
- Deal creation events
- Deal stage changes
- Call count updates

**Supported Event Types:**
- `deal.created` - New deal notifications
- `deal.stage_changed` - Stage progression updates
- `call.count` - Call activity tracking

## Configuration

### Lead Sources
Lead sources are configured with:
- **Name and Description**: Human-readable identifiers
- **SLA Rules**: Call timing intervals (array of time offsets)
- **Max Calls**: Maximum number of call attempts
- **Ranking**: Priority/importance scoring
- **Status**: Enable/disable flag

### Deal Stages
Deal stages support:
- **Stage ID**: HubSpot stage identifier
- **Label**: Human-readable stage name
- **Pipeline**: Associated sales pipeline

## Monitoring & Observability

### Logging
Comprehensive logging for:
- Webhook event processing
- SLA calculations and breaches
- HubSpot API interactions
- Error conditions and recovery

### Metrics
Key metrics tracked:
- Deal creation rates
- Stage progression times
- SLA breach rates
- Lead source performance

## Best Practices

### SLA Management
- Configure realistic call intervals
- Monitor breach rates regularly
- Adjust lead source rules based on performance
- Consider business hours in calculations

### HubSpot Integration
- Validate webhook signatures
- Implement idempotent processing
- Handle API rate limits gracefully
- Log all integration events

### Performance
- Use database indexes for common queries
- Cache active lead sources
- Process webhooks asynchronously
- Monitor queue performance

## Next Steps

- [Entities Documentation](./entities.md) - Detailed model information
- [API Reference](./api.md) - Webhook endpoints and integration
- [Radar Domain](../radar/) - Document analysis features
- [Suitability Reports](../suitability-reports/) - Customer assessment tools
