# Radar Domain - API Reference

The Radar domain provides APIs for document analysis, OCR processing, and intelligent scoring. Most functionality is accessed through web interfaces, with some internal APIs for integration.

## OCR API

### Document OCR Processing

```http
POST /api/ocr
```

Processes uploaded documents for text extraction using OCR technology.

**Authentication**: Required (Bearer token)

**Content-Type**: `multipart/form-data`

**Request Parameters:**
- `file` (required): Document file (PDF, PNG, JPG, etc.)
- `reference` (optional): Custom reference for the document

**Response:**
```json
{
  "status": "success",
  "data": {
    "document_id": "01HN123ABC456DEF789",
    "reference": "DOC-2024-001",
    "processing_status": "queued",
    "estimated_completion": "2024-01-15T10:35:00Z"
  },
  "message": "Document uploaded and queued for processing"
}
```

**Error Responses:**

**File Too Large (413):**
```json
{
  "error": "File too large",
  "message": "Maximum file size is 10MB",
  "max_size": "10MB"
}
```

**Unsupported Format (422):**
```json
{
  "error": "Unsupported file format",
  "message": "Supported formats: PDF, PNG, JPG, JPEG, TIFF",
  "supported_formats": ["pdf", "png", "jpg", "jpeg", "tiff"]
}
```

## Internal APIs

### Document Management

#### Get Document by Reference

```php
// Service method
public function getDocumentByReference(string $reference): ?Document
{
    return Document::where('reference', $reference)->first();
}
```

#### Get Radar Result

```php
// Service method
public function getRadarResult(string $documentId): ?RadarResult
{
    return RadarResult::where('document_id', $documentId)
        ->with(['tags.definition', 'insights.definition'])
        ->first();
}
```

### Analysis Status API

#### Check Processing Status

```php
// Service method
public function getProcessingStatus(string $documentId): array
{
    $result = RadarResult::find($documentId);
    
    return [
        'status' => $result->status ?? 'pending',
        'progress' => $this->calculateProgress($result),
        'completed_steps' => $this->getCompletedSteps($result),
        'estimated_completion' => $this->estimateCompletion($result)
    ];
}
```

**Progress Calculation:**
```php
private function calculateProgress(RadarResult $result): int
{
    $steps = [
        'text_extracted' => !empty($result->ocr_text),
        'tags_extracted' => $result->tags()->exists(),
        'insights_generated' => $result->insights()->exists(),
        'scores_calculated' => $result->scores_calculated,
        'summary_generated' => !empty($result->summary),
        'analysis_complete' => !empty($result->ollie_thinks)
    ];
    
    $completed = array_sum($steps);
    $total = count($steps);
    
    return round(($completed / $total) * 100);
}
```

## Export API

### Generate Export

```php
// Service method
public function generateExport(?int $tagFilter = null): string
{
    $export = new RadarExport($tagFilter);
    $filename = 'radar_export_' . now()->format('Y-m-d_H-i-s') . '.xlsx';
    
    return $export->store($filename, 'exports');
}
```

**Export Structure:**
- **Header Sheet**: Document metadata and basic information
- **Tags Sheet**: All extracted tags and their values
- **Insights Sheet**: Generated insights and recommendations

### Download Export

```http
GET /radar/export/{filename}
```

**Authentication**: Required

**Response**: Excel file download

## Scoring API

### Get Document Score

```php
// Service method
public function getDocumentScore(string $documentId): array
{
    $result = RadarResult::find($documentId);
    
    if (!$result || !$result->scores_calculated) {
        return ['error' => 'Scores not yet calculated'];
    }
    
    return [
        'total_score' => $result->score,
        'max_possible_score' => $this->getMaxPossibleScore(),
        'score_percentage' => ($result->score / $this->getMaxPossibleScore()) * 100,
        'detailed_scores' => $result->scores,
        'score_breakdown' => $this->getScoreBreakdown($result->scores)
    ];
}
```

**Score Breakdown:**
```json
{
  "total_score": 85,
  "max_possible_score": 100,
  "score_percentage": 85.0,
  "detailed_scores": [
    {
      "rule": "has_trust",
      "score": 10,
      "weight": 10,
      "details": "Trust provisions found"
    },
    {
      "rule": "has_professional_executor",
      "score": 8,
      "weight": 10,
      "details": "Professional executor appointed"
    }
  ],
  "score_breakdown": {
    "excellent": ["has_trust", "has_professional_executor"],
    "good": ["will_date", "has_children"],
    "needs_improvement": ["has_property"],
    "missing": []
  }
}
```

## Tag Management API

### Get Available Tags

```php
// Service method
public function getAvailableTags(): Collection
{
    return RadarTag::orderBy('category')
        ->orderBy('name')
        ->get()
        ->groupBy('category');
}
```

### Search Documents by Tags

```php
// Service method
public function searchByTags(array $tagSlugs): Collection
{
    return RadarResult::whereHas('tags', function ($query) use ($tagSlugs) {
        $query->whereHas('definition', function ($subQuery) use ($tagSlugs) {
            $subQuery->whereIn('slug', $tagSlugs);
        });
    })->with(['document', 'tags.definition'])->get();
}
```

## Insight API

### Get Document Insights

```php
// Service method
public function getDocumentInsights(string $documentId): Collection
{
    return RadarResultInsight::where('radar_result_id', $documentId)
        ->with('definition')
        ->orderBy('confidence', 'desc')
        ->get();
}
```

**Insight Structure:**
```json
{
  "id": "01HN123ABC456DEF789",
  "insight_type": "will_validity",
  "title": "Will Validity Assessment",
  "content": "The will appears to be properly executed with appropriate witness signatures...",
  "confidence": 0.92,
  "category": "legal_compliance",
  "metadata": {
    "sources": ["signature_analysis", "witness_requirements"],
    "risk_level": "low"
  }
}
```

## Processing Pipeline API

### Trigger Manual Processing

```php
// Service method
public function triggerProcessing(string $documentId): bool
{
    $result = RadarResult::find($documentId);
    
    if (!$result) {
        return false;
    }
    
    // Trigger the processing pipeline
    ConvertWillToPng::withChain([
        new ExtractText($result),
        new ExtractTags($result),
        new GenerateInsights($result),
        new GenerateSummary($result),
        new CalculateScore($result),
        new OllieThinks($result),
    ])->dispatch($result);
    
    return true;
}
```

### Retry Failed Processing

```php
// Service method
public function retryFailedStep(string $documentId, string $step): bool
{
    $result = RadarResult::find($documentId);
    
    if (!$result) {
        return false;
    }
    
    $jobClass = $this->getJobClassForStep($step);
    
    if (!$jobClass) {
        return false;
    }
    
    dispatch(new $jobClass($result));
    
    return true;
}
```

## WebSocket Events

### Real-time Processing Updates

The Radar domain broadcasts real-time updates during document processing:

**Event Types:**
- `processing.started` - Processing pipeline initiated
- `processing.step_completed` - Individual step completed
- `processing.completed` - All processing finished
- `processing.failed` - Processing failed

**Event Structure:**
```json
{
  "event": "processing.step_completed",
  "document_id": "01HN123ABC456DEF789",
  "step": "extract_text",
  "progress": 33,
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "step_name": "Text Extraction",
    "duration": "15.2s",
    "next_step": "Extract Tags"
  }
}
```

## Error Handling

### Processing Errors

**OCR Failure:**
```json
{
  "error": "OCR processing failed",
  "code": "OCR_FAILED",
  "message": "Unable to extract text from document",
  "retry_available": true,
  "suggested_action": "Check document quality and try again"
}
```

**AI Service Error:**
```json
{
  "error": "AI service unavailable",
  "code": "AI_SERVICE_ERROR",
  "message": "OpenAI API temporarily unavailable",
  "retry_available": true,
  "estimated_retry_time": "2024-01-15T10:45:00Z"
}
```

**Scoring Error:**
```json
{
  "error": "Scoring calculation failed",
  "code": "SCORING_ERROR",
  "message": "Unable to calculate document score",
  "affected_rules": ["has_trust", "will_date"],
  "partial_results_available": true
}
```

## Rate Limiting

### API Limits
- **OCR Processing**: 10 documents per minute per user
- **Export Generation**: 5 exports per hour per user
- **Analysis Requests**: 100 requests per minute per user

### Queue Limits
- **Processing Queue**: Maximum 50 documents in queue per user
- **Priority Processing**: Premium users get priority queue access
- **Batch Processing**: Support for batch document processing

## Monitoring & Metrics

### Processing Metrics
- **Average Processing Time**: Time from upload to completion
- **Success Rate**: Percentage of successfully processed documents
- **Error Rate**: Rate of processing failures by type
- **Queue Length**: Current processing queue length

### Quality Metrics
- **OCR Accuracy**: Text extraction accuracy scores
- **AI Confidence**: Average confidence scores for insights
- **Score Distribution**: Distribution of document scores
- **Tag Accuracy**: Manual validation of tag extraction

## Best Practices

### Document Upload
- **File Size**: Keep files under 10MB for optimal processing
- **Image Quality**: Use high-resolution images for better OCR results
- **File Format**: PDF format generally provides best results
- **Document Orientation**: Ensure documents are properly oriented

### API Usage
- **Polling**: Use reasonable polling intervals for status checks
- **Error Handling**: Implement proper retry logic for transient failures
- **Caching**: Cache results to avoid unnecessary API calls
- **Rate Limiting**: Respect rate limits and implement backoff strategies
