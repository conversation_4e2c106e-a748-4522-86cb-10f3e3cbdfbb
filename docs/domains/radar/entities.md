# Radar Domain - Entities

The Radar domain contains entities for document analysis, scoring, tagging, and insight generation.

## Core Models

### Document
**Location**: `App\Domains\Radar\Models\Document`

The base entity representing an uploaded document for analysis.

**Key Properties:**
```php
protected $guarded = []; // Mass assignment protection disabled

// Uses ULIDs for unique identification
use HasUlids;
```

**Relationships:**
```php
// Has one radar result
public function radarResult(): HasOne
{
    return $this->hasOne(RadarResult::class);
}
```

**Usage:**
- Stores document metadata and file information
- Serves as the entry point for document processing
- Links to analysis results through RadarResult relationship

### RadarResult
**Location**: `App\Domains\Radar\Models\RadarResult`

The central entity containing all analysis results for a document.

**Key Properties:**
```php
protected $guarded = []; // Mass assignment protection disabled

protected $casts = [
    'scores' => 'array', // JSON array of scoring results
];
```

**Traits:**
```php
use HasFactory;
use HasTags;      // Custom tagging functionality
use HasUlids;     // UUID-based primary keys
```

**Relationships:**
```php
// Belongs to a document
public function document(): BelongsTo
{
    return $this->belongsTo(Document::class);
}

// Has many tags
public function tags(): HasMany
{
    return $this->hasMany(RadarResultTag::class);
}

// Has many insights
public function insights(): HasMany
{
    return $this->hasMany(RadarResultInsight::class);
}
```

**Key Methods:**

#### findTag()
Finds a specific tag by its slug:
```php
public function findTag($tag)
{
    $tag = $this->tags->filter(function ($item) use ($tag) {
        return $item->definition->slug === $tag;
    })->first();

    if ($tag) {
        return $tag->value;
    }

    return false;
}
```

**Observer**: `App\Domains\Radar\Observers\RadarResultObserver`

**Key Fields:**
- `ocr_text` - Extracted text from OCR processing
- `scores` - JSON array of calculated scores
- `scores_calculated` - Boolean flag for completion status
- `summary` - AI-generated document summary
- `ollie_thinks` - AI analysis and recommendations

### RadarTag
**Location**: `App\Domains\Radar\Models\RadarTag`

Defines available tags that can be applied to documents.

**Key Properties:**
```php
use HasUlids;

// Tag definition fields
protected $fillable = [
    'name',         // Human-readable tag name
    'slug',         // URL-friendly identifier
    'description',  // Tag description
    'category',     // Tag category/group
    'data_type',    // Expected value type (string, boolean, number)
];
```

**Relationships:**
```php
// Has many result tags (usage instances)
public function taggedIn(): HasMany
{
    return $this->hasMany(RadarResultTag::class);
}
```

**Usage:**
- Defines the vocabulary of available tags
- Provides metadata about tag types and categories
- Enables consistent tagging across documents

### RadarResultTag
**Location**: `App\Domains\Radar\Models\RadarResultTag`

Links RadarResults to RadarTags with specific values.

**Key Properties:**
```php
use HasFactory;
use HasUlids;

protected $guarded = [];

// Eager load the tag definition
protected $with = ['definition'];
```

**Relationships:**
```php
// Belongs to a tag definition
public function definition(): BelongsTo
{
    return $this->belongsTo(RadarTag::class, 'radar_tag_id');
}
```

**Key Fields:**
- `radar_result_id` - Links to the analysis result
- `radar_tag_id` - Links to the tag definition
- `value` - The actual tag value (string, boolean, number)
- `confidence` - AI confidence score (0-1)
- `context` - Additional context or metadata

### RadarInsight
**Location**: `App\Domains\Radar\Models\RadarInsight`

Defines available insight types that can be generated.

**Key Properties:**
```php
use HasUlids;

protected $guarded = [];
```

**Key Fields:**
- `name` - Insight name/title
- `slug` - URL-friendly identifier
- `description` - Insight description
- `prompt_template` - AI prompt template for generation
- `category` - Insight category/group

### RadarResultInsight
**Location**: `App\Domains\Radar\Models\RadarResultInsight`

Generated insights for specific documents.

**Key Properties:**
```php
use HasUlids;

protected $guarded = [];
```

**Relationships:**
```php
// Belongs to an insight definition
public function definition(): BelongsTo
{
    return $this->belongsTo(RadarInsight::class, 'radar_insight_id');
}
```

**Key Fields:**
- `radar_result_id` - Links to the analysis result
- `radar_insight_id` - Links to the insight definition
- `content` - Generated insight content
- `confidence` - AI confidence score
- `metadata` - Additional insight metadata

## Traits

### HasTags
**Location**: `App\Domains\Radar\Models\Traits\HasTags`

Custom trait providing tagging functionality for RadarResult.

**Key Methods:**

#### attachTags()
```php
public function attachTags($tags, $locale = null): static
```

#### detachTags()
```php
public function detachTags($tags = null, $locale = null): static
```

#### syncTags()
```php
public function syncTags($tags, $locale = null): static
```

**Usage:**
- Provides flexible tagging operations
- Supports tag synchronization
- Handles tag value assignment

## Enumerations

### RadarStatus
**Location**: `App\Domains\Radar\Enums\RadarStatus`

Defines processing status for radar results.

```php
enum RadarStatus: string
{
    case PENDING = 'pending';
    case PROCESSING = 'processing';
    case COMPLETED = 'completed';
    case FAILED = 'failed';
}
```

## Job Pipeline

### Processing Jobs
The Radar domain uses a sophisticated job pipeline for document processing:

#### ConvertWillToPng
**Location**: `App\Domains\Radar\Jobs\Radar\ConvertWillToPng`

Converts uploaded documents to PNG format for OCR processing.

#### ExtractText
**Location**: `App\Domains\Radar\Jobs\Radar\ExtractText`

Performs OCR text extraction from converted images.

#### ExtractTags
**Location**: `App\Domains\Radar\Jobs\Radar\ExtractTags`

AI-powered tag extraction and categorization.

#### GenerateInsights
**Location**: `App\Domains\Radar\Jobs\Radar\GenerateInsights`

Creates AI-generated insights about document content.

**Key Implementation:**
```php
public function handle(): void
{
    $insights = RadarInsight::all();
    $json = $insights->toJson();

    $result = OpenAI::chat()->create([
        'model' => 'gpt-4o-mini',
        'response_format' => ['type' => 'json_object'],
        'messages' => [
            ['role' => 'system', 'content' => file_get_contents(resource_path('data/system_prompt.txt'))],
            ['role' => 'system', 'content' => 'Will Text: ' . $this->result->ocr_text],
            ['role' => 'system', 'content' => 'Insight Definitions: ' . $json],
            ['role' => 'user', 'content' => file_get_contents(resource_path('data/insight_prompt.txt'))],
        ],
    ]);
    
    // Process and store insights
}
```

#### CalculateScore
**Location**: `App\Domains\Radar\Jobs\Radar\CalculateScore`

Applies scoring rules through a pipeline system.

**Scoring Pipeline:**
```php
public function handle(): void
{
    $result = app(Pipeline::class)
        ->send($this->result)
        ->through([
            HasTrust::class,
            HasProfessionalExecutor::class,
            WillDate::class,
            HasNillRateBand::class,
            HasChildren::class,
            HasPets::class,
            HasDisability::class,
            IsMarried::class,
            HasProperty::class,
        ])->thenReturn();

    $score = collect($result->scores)->pluck('score')->sum();

    $this->result->update([
        'score' => $score,
        'scores_calculated' => true,
    ]);
}
```

## Scoring Rules

### Rule Structure
Each scoring rule implements a consistent interface:

```php
class HasTrust
{
    public function handle(RadarResult $result, Closure $next): RadarResult
    {
        // Analyze document for trust-related content
        $score = $this->calculateTrustScore($result->ocr_text);
        
        // Add score to results
        $scores = $result->scores ?? [];
        $scores[] = [
            'rule' => 'has_trust',
            'score' => $score,
            'weight' => 10,
            'details' => $this->getScoreDetails()
        ];
        
        $result->scores = $scores;
        
        return $next($result);
    }
}
```

### Available Rules
- **HasTrust**: Checks for trust-related provisions
- **HasProfessionalExecutor**: Identifies professional executor appointments
- **WillDate**: Analyzes will date and validity
- **HasNillRateBand**: Checks for nil rate band provisions
- **HasChildren**: Identifies references to children/beneficiaries
- **HasPets**: Detects pet-related provisions
- **HasDisability**: Identifies disability considerations
- **IsMarried**: Determines marital status references
- **HasProperty**: Analyzes property-related content

## Export System

### RadarExport
**Location**: `App\Domains\Radar\Services\Export\Radar\RadarExport`

Provides multi-sheet Excel export functionality.

**Implementation:**
```php
class RadarExport implements WithMultipleSheets
{
    use Exportable;

    public function sheets(): array
    {
        return [
            'Header' => new HeaderSheet($this->results),
            'Tags' => new TagsSheet($this->results),
            'Insights' => new InsightsSheet($this->results),
        ];
    }

    public function getResults(): Collection
    {
        return RadarResult::query()
            ->whereRaw('length(ocr_text) > 50')
            ->tap(fn ($query) => $this->tag ? $query->whereHas('documentTags',
                fn ($query) => $query->where('id', $this->tag)) : $query)
            ->with(['tags', 'insights', 'document'])
            ->get();
    }
}
```

## Database Relationships

```mermaid
erDiagram
    DOCUMENT {
        id string PK
        reference string UK
        filename string
        file_path string
        created_at timestamp
        updated_at timestamp
    }
    
    RADAR_RESULT {
        id string PK
        document_id string FK
        ocr_text text
        scores json
        scores_calculated boolean
        summary text
        ollie_thinks text
        created_at timestamp
        updated_at timestamp
    }
    
    RADAR_TAG {
        id string PK
        name string
        slug string UK
        description text
        category string
        data_type string
        created_at timestamp
        updated_at timestamp
    }
    
    RADAR_RESULT_TAG {
        id string PK
        radar_result_id string FK
        radar_tag_id string FK
        value text
        confidence decimal
        context json
        created_at timestamp
        updated_at timestamp
    }
    
    RADAR_INSIGHT {
        id string PK
        name string
        slug string UK
        description text
        prompt_template text
        category string
        created_at timestamp
        updated_at timestamp
    }
    
    RADAR_RESULT_INSIGHT {
        id string PK
        radar_result_id string FK
        radar_insight_id string FK
        content text
        confidence decimal
        metadata json
        created_at timestamp
        updated_at timestamp
    }
    
    DOCUMENT ||--|| RADAR_RESULT : "has one"
    RADAR_RESULT ||--o{ RADAR_RESULT_TAG : "has many"
    RADAR_RESULT ||--o{ RADAR_RESULT_INSIGHT : "has many"
    RADAR_TAG ||--o{ RADAR_RESULT_TAG : "used in"
    RADAR_INSIGHT ||--o{ RADAR_RESULT_INSIGHT : "generates"
```

## Performance Considerations

### Indexing
Key database indexes:
- `document.reference` (unique)
- `radar_result.document_id` (foreign key)
- `radar_result_tag.radar_result_id` (foreign key)
- `radar_result_insight.radar_result_id` (foreign key)

### Caching
- OCR results cached to avoid reprocessing
- Tag definitions cached for quick lookup
- Export results cached temporarily
- AI responses cached to reduce API calls

### Queue Processing
- All processing jobs run asynchronously
- Job chaining for pipeline processing
- Failed job retry with exponential backoff
- Progress tracking for user feedback
