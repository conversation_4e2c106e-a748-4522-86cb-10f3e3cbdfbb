# Radar Domain

The Radar domain provides intelligent document analysis and scoring capabilities, particularly focused on legal documents like wills. It combines OCR technology, AI-powered analysis, and rule-based scoring to extract insights and assess document quality.

## Purpose

The Radar domain handles:
- **Document Processing**: OCR text extraction from uploaded documents
- **AI Analysis**: OpenAI-powered document analysis and insight generation
- **Scoring System**: Rule-based scoring for document quality assessment
- **Tag Extraction**: Automated categorization and tagging of documents
- **Insight Generation**: AI-generated insights about document content
- **Export Capabilities**: Comprehensive data export for analysis

## Key Features

### Document Analysis Pipeline
- **File Upload**: Support for various document formats (PDF, images)
- **OCR Processing**: Text extraction using advanced OCR technology
- **AI Analysis**: OpenAI integration for content understanding
- **Tag Extraction**: Automated identification of key document elements
- **Scoring**: Multi-criteria scoring based on configurable rules
- **Insight Generation**: AI-powered insights and recommendations

### Intelligent Scoring
- **Rule-Based System**: Configurable scoring rules for different criteria
- **Multi-Factor Analysis**: Considers multiple document aspects
- **Weighted Scoring**: Different rules have different importance weights
- **Progressive Calculation**: Scores calculated through pipeline processing
- **Breach Detection**: Identifies potential issues or concerns

### Tag Management
- **Dynamic Tagging**: AI-powered tag extraction from document content
- **Tag Definitions**: Configurable tag types and categories
- **Relationship Mapping**: Tags linked to specific document sections
- **Search & Filter**: Tag-based document discovery and filtering

## Architecture

```mermaid
graph TB
    subgraph "Radar Domain"
        subgraph "Models"
            Doc[Document]
            Result[Radar Result]
            Tag[Radar Tag]
            ResultTag[Radar Result Tag]
            Insight[Radar Insight]
            ResultInsight[Radar Result Insight]
        end
        
        subgraph "Services"
            OCR[OCR Service]
            AI[AI Analysis Service]
            Score[Scoring Service]
            Export[Export Service]
        end
        
        subgraph "Jobs Pipeline"
            Convert[Convert to PNG]
            Extract[Extract Text]
            ExtractTags[Extract Tags]
            GenInsights[Generate Insights]
            CalcScore[Calculate Score]
            Summary[Generate Summary]
            Think[Ollie Thinks]
        end
    end
    
    subgraph "External Services"
        OpenAI[OpenAI API]
        Storage[File Storage]
    end
    
    Doc --> Result
    Result --> ResultTag
    Result --> ResultInsight
    ResultTag --> Tag
    ResultInsight --> Insight
    
    Doc --> Convert
    Convert --> Extract
    Extract --> ExtractTags
    ExtractTags --> GenInsights
    GenInsights --> CalcScore
    CalcScore --> Summary
    Summary --> Think
    
    OCR --> Extract
    AI --> GenInsights
    AI --> Summary
    AI --> Think
    OpenAI --> AI
```

## Core Entities

### Document
The base entity representing an uploaded document:
- **File Management**: Handles document storage and retrieval
- **Reference System**: Unique reference numbers for tracking
- **Metadata**: Document type, size, upload information
- **Processing Status**: Tracks document through analysis pipeline

### RadarResult
The central analysis result entity:
- **OCR Text**: Extracted text content from document
- **Scores**: Calculated scores from various rules
- **Status Tracking**: Processing status and completion flags
- **AI Analysis**: Generated summaries and insights
- **Relationships**: Links to tags and insights

### Tagging System
- **RadarTag**: Tag definitions and categories
- **RadarResultTag**: Links between results and tags with values
- **Dynamic Values**: Tags can have associated values or metadata

### Insights System
- **RadarInsight**: Insight definitions and templates
- **RadarResultInsight**: Generated insights for specific documents
- **AI-Powered**: Insights generated through OpenAI analysis

## Processing Pipeline

### Document Upload & Processing
1. **Document Upload**: User uploads document through web interface
2. **Reference Generation**: Unique reference number assigned
3. **Initial Storage**: Document stored in file system
4. **Pipeline Trigger**: Processing pipeline automatically initiated

### Analysis Pipeline
The domain uses a sophisticated job pipeline for document analysis:

1. **ConvertWillToPng**: Converts documents to PNG format for OCR
2. **ExtractText**: Performs OCR to extract text content
3. **ExtractTags**: AI-powered tag extraction and categorization
4. **GenerateInsights**: Creates AI-generated insights about content
5. **GenerateSummary**: Produces document summary
6. **CalculateScore**: Applies scoring rules to assess quality
7. **OllieThinks**: Final AI analysis and recommendations

### Scoring Rules
The scoring system uses a pipeline of configurable rules:

- **HasTrust**: Checks for trust-related content
- **HasProfessionalExecutor**: Identifies professional executor appointments
- **WillDate**: Analyzes will date and validity
- **HasNillRateBand**: Checks for nil rate band provisions
- **HasChildren**: Identifies references to children/beneficiaries
- **HasPets**: Detects pet-related provisions
- **HasDisability**: Identifies disability considerations
- **IsMarried**: Determines marital status references
- **HasProperty**: Analyzes property-related content

## AI Integration

### OpenAI Services
The domain integrates with OpenAI for:
- **Content Analysis**: Understanding document structure and content
- **Insight Generation**: Creating meaningful insights about documents
- **Tag Extraction**: Identifying relevant tags and categories
- **Summary Generation**: Producing concise document summaries
- **Quality Assessment**: AI-powered quality evaluation

### Prompt Engineering
- **System Prompts**: Carefully crafted prompts for consistent analysis
- **Context Injection**: Document text and definitions provided as context
- **Structured Output**: JSON-formatted responses for reliable parsing
- **Error Handling**: Robust handling of AI service failures

## Export & Reporting

### Multi-Sheet Export
The domain provides comprehensive export capabilities:
- **Header Sheet**: Document metadata and basic information
- **Tags Sheet**: All extracted tags and their values
- **Insights Sheet**: Generated insights and recommendations
- **Filtering**: Export can be filtered by tags or other criteria

### Data Analysis
- **Performance Metrics**: Scoring statistics and trends
- **Tag Analysis**: Frequency and distribution of tags
- **Quality Metrics**: Document quality assessments
- **Comparative Analysis**: Cross-document comparisons

## Web Interface

### Document Management
- **Upload Interface**: Drag-and-drop document upload
- **Processing Status**: Real-time processing status updates
- **Result Viewing**: Comprehensive result display with tabs
- **Search & Filter**: Find documents by tags, scores, or content

### Analysis Display
- **Tabbed Interface**: Organized display of tags, insights, and scores
- **Interactive Elements**: Clickable tags and expandable insights
- **Visual Indicators**: Color-coded status and quality indicators
- **Export Options**: One-click export functionality

## Configuration

### Scoring Rules
Scoring rules are configurable and can be:
- **Enabled/Disabled**: Turn rules on or off
- **Weighted**: Assign different importance to rules
- **Customized**: Modify rule logic and thresholds
- **Extended**: Add new rules to the pipeline

### AI Configuration
- **Model Selection**: Choose OpenAI models for different tasks
- **Prompt Templates**: Customize prompts for specific use cases
- **Response Formats**: Configure output formats and structures
- **Rate Limiting**: Manage API usage and costs

## Performance & Scalability

### Asynchronous Processing
- **Queue-Based**: All processing happens in background jobs
- **Pipeline Architecture**: Jobs chained for efficient processing
- **Error Recovery**: Failed jobs can be retried automatically
- **Progress Tracking**: Real-time status updates for users

### Caching Strategy
- **Result Caching**: Processed results cached for quick access
- **Tag Caching**: Frequently used tags cached in memory
- **Export Caching**: Generated exports cached temporarily
- **Invalidation**: Smart cache invalidation on updates

## Security & Privacy

### Data Protection
- **Secure Storage**: Documents stored with appropriate security
- **Access Control**: User-based access to documents and results
- **Audit Logging**: Comprehensive logging of all operations
- **Data Retention**: Configurable retention policies

### AI Privacy
- **Data Minimization**: Only necessary data sent to AI services
- **No Training**: Explicit opt-out from AI model training
- **Secure Transmission**: Encrypted communication with AI services
- **Result Sanitization**: AI responses sanitized before storage

## Next Steps

- [Entities Documentation](./entities.md) - Detailed model information
- [API Reference](./api.md) - Available endpoints and integration
- [Suitability Reports](../suitability-reports/) - Customer assessment tools
- [Global Domain](../global/) - Shared services and utilities
