# Suitability Reports Domain - API Reference

The Suitability Reports domain provides APIs for customer management, assessment handling, and HubSpot integration.

## HubSpot Integration API

### Start Suitability Report from HubSpot

```http
GET /internal-suitability-report/hubspot/{hubspotId}
```

Creates or retrieves a suitability report customer from a HubSpot contact.

**Authentication**: Required (session-based)

**Parameters:**
- `hubspotId` (string): HubSpot contact ID (alphanumeric)

**Response:**
- **Success**: Redirects to suitability report interface
- **Error**: Returns appropriate error page

**Flow:**
1. Check if customer already exists with HubSpot ID
2. If not found, fetch contact data from HubSpot
3. Create new customer record with HubSpot data
4. Auto-create associated suitability report
5. Redirect to assessment interface

**Error Handling:**

**Contact Not Found (404):**
```html
<!-- Error page displayed -->
<h1>Contact Not Found</h1>
<p>The HubSpot contact could not be found or accessed.</p>
```

**API Error (500):**
```html
<!-- Error page displayed -->
<h1>Service Unavailable</h1>
<p>Unable to retrieve contact information. Please try again later.</p>
```

## Internal APIs

### Customer Management

#### Get Customer by ID

```php
// Service method
public function getCustomerById(string $customerId): ?SuitabilityReportCustomer
{
    return SuitabilityReportCustomer::with(['report', 'estatePlanner'])
        ->find($customerId);
}
```

#### Get Customer by HubSpot ID

```php
// Service method
public function getCustomerByHubSpotId(string $hubspotId): ?SuitabilityReportCustomer
{
    return SuitabilityReportCustomer::where('hubspot_id', $hubspotId)
        ->with(['report', 'estatePlanner'])
        ->first();
}
```

#### Create Customer from HubSpot

```php
// Service method
public function createCustomerFromHubSpot(
    string $hubspotId, 
    int $estatePlannerId
): SuitabilityReportCustomer {
    $hubSpotService = app(HubSpotApiService::class);
    $contact = $hubSpotService->getContactById($hubspotId);
    $properties = $contact->getProperties();
    
    $firstName = $properties['firstname'] ?? '';
    $lastName = $properties['lastname'] ?? '';
    $fullName = trim("{$firstName} {$lastName}");
    $email = $properties['email'] ?? '';
    
    return SuitabilityReportCustomer::create([
        'hubspot_id' => $hubspotId,
        'name' => $fullName ?: $email,
        'estate_planner_id' => $estatePlannerId,
    ]);
}
```

### Assessment Management

#### Get Assessment Data

```php
// Service method
public function getAssessmentData(string $customerId): ?SuitabilityReport
{
    return SuitabilityReport::where('customer_id', $customerId)
        ->with(['suitabilityReportCustomer', 'recommendation'])
        ->first();
}
```

#### Update Assessment

```php
// Service method
public function updateAssessment(
    string $customerId, 
    array $assessmentData
): SuitabilityReport {
    $report = SuitabilityReport::where('customer_id', $customerId)->first();
    
    if (!$report) {
        throw new ModelNotFoundException('Suitability report not found');
    }
    
    $report->update($assessmentData);
    
    // Trigger recommendation generation if assessment is complete
    if ($this->isAssessmentComplete($report)) {
        $this->generateRecommendations($report);
    }
    
    return $report->fresh(['recommendation']);
}
```

#### Check Assessment Completion

```php
// Service method
public function isAssessmentComplete(SuitabilityReport $report): bool
{
    $requiredFields = [
        'relationship_status',
        'child_count',
        'property_count',
        'estate_value',
        'has_will',
        'has_lpa'
    ];
    
    foreach ($requiredFields as $field) {
        if (is_null($report->$field)) {
            return false;
        }
    }
    
    return true;
}
```

### Recommendation Engine

#### Generate Recommendations

```php
// Service method
public function generateRecommendations(SuitabilityReport $report): SuitabilityReportRecommendation
{
    $recommendations = [
        'will' => $this->generateWillRecommendation($report),
        'trusts' => $this->generateTrustRecommendation($report),
        'lpa' => $this->generateLpaRecommendation($report),
        'iht' => $this->generateIhtRecommendation($report),
    ];
    
    return $report->recommendation()->updateOrCreate([], $recommendations);
}
```

#### Will Recommendation Logic

```php
private function generateWillRecommendation(SuitabilityReport $report): array
{
    if (!$report->has_will) {
        return [
            'priority' => 'high',
            'title' => 'Will Creation Required',
            'description' => 'You do not currently have a will. This is essential for ensuring your assets are distributed according to your wishes.',
            'action_items' => [
                'Schedule will consultation',
                'Gather asset information',
                'Consider executor appointments',
                'Review beneficiary designations'
            ],
            'estimated_cost' => '£500-£800',
            'timeframe' => '2-4 weeks',
            'urgency_reason' => 'Without a will, your estate will be distributed according to intestacy rules'
        ];
    }
    
    // Additional logic for will updates, reviews, etc.
    return $this->generateWillUpdateRecommendation($report);
}
```

#### Trust Recommendation Logic

```php
private function generateTrustRecommendation(SuitabilityReport $report): ?array
{
    $shouldRecommendTrust = false;
    $trustType = null;
    $reasons = [];
    
    // Life interest trust for married couples with children
    if ($report->relationship_status === 'married' && $report->child_count > 0) {
        $shouldRecommendTrust = true;
        $trustType = 'life_interest_trust';
        $reasons[] = 'Married with children - life interest trust protects inheritance';
    }
    
    // Trust for children under 18
    if ($report->children_under_18) {
        $shouldRecommendTrust = true;
        $trustType = 'children_trust';
        $reasons[] = 'Children under 18 require trust protection';
    }
    
    // Trust for step-family situations
    if ($report->step_children) {
        $shouldRecommendTrust = true;
        $trustType = 'step_family_trust';
        $reasons[] = 'Step-family situation benefits from trust structure';
    }
    
    // Trust for disability considerations
    if ($report->child_with_disability_or_care_needs) {
        $shouldRecommendTrust = true;
        $trustType = 'disability_trust';
        $reasons[] = 'Child with disability requires specialized trust planning';
    }
    
    if (!$shouldRecommendTrust) {
        return null;
    }
    
    return [
        'priority' => 'medium',
        'title' => 'Trust Planning Recommended',
        'description' => 'Based on your circumstances, trust planning could provide significant benefits.',
        'trust_type' => $trustType,
        'reasons' => $reasons,
        'action_items' => [
            'Discuss trust options with estate planner',
            'Review tax implications',
            'Consider trustee appointments',
            'Plan trust funding strategy'
        ],
        'estimated_cost' => '£1,500-£3,000',
        'timeframe' => '4-8 weeks'
    ];
}
```

## Form Handling API

### Livewire Form Integration

The domain uses Livewire forms for real-time assessment handling:

#### Form Validation

```php
// Form validation rules
public function rules(): array
{
    return [
        'partner_name' => 'nullable|string|max:255',
        'relationship_status' => [
            'nullable',
            Rule::in(['single', 'married', 'civil_partnership', 'divorced', 'widowed', 'separated'])
        ],
        'child_count' => 'nullable|integer|min:0|max:20',
        'children_under_18' => 'nullable|boolean',
        'step_children' => 'nullable|boolean',
        'child_with_disability_or_care_needs' => 'nullable|boolean',
        'property_count' => 'nullable|integer|min:0|max:50',
        'owns_business' => 'nullable|boolean',
        'estate_value' => [
            'nullable',
            Rule::in(['under_325k', '325k_650k', '650k_1m', '1m_2m', 'over_2m'])
        ],
        'has_will' => 'nullable|boolean',
        'has_trust' => 'nullable|boolean',
        'has_lpa' => 'nullable|boolean',
        'partner_has_will' => 'nullable|boolean',
        'partner_has_lpa' => 'nullable|boolean',
        'marketing_preference' => [
            'nullable',
            Rule::in(['email', 'phone', 'post', 'no_marketing'])
        ],
        'notes' => 'nullable|string|max:2000',
    ];
}
```

#### Auto-Save Functionality

```php
// Livewire component method
public function updated($propertyName): void
{
    $this->validateOnly($propertyName);
    
    // Auto-save to database
    $this->report->update([
        $propertyName => $this->$propertyName
    ]);
    
    // Check if assessment is now complete
    if ($this->isAssessmentComplete()) {
        $this->generateRecommendations();
    }
    
    // Emit progress update
    $this->dispatch('assessment-updated', [
        'progress' => $this->calculateProgress(),
        'field' => $propertyName
    ]);
}
```

## Data Export API

### Export Assessment Data

```php
// Service method
public function exportAssessmentData(string $customerId, string $format = 'pdf'): string
{
    $customer = SuitabilityReportCustomer::with(['report.recommendation'])
        ->findOrFail($customerId);
    
    switch ($format) {
        case 'pdf':
            return $this->generatePdfReport($customer);
        case 'json':
            return $this->generateJsonExport($customer);
        case 'csv':
            return $this->generateCsvExport($customer);
        default:
            throw new InvalidArgumentException("Unsupported format: {$format}");
    }
}
```

### PDF Report Generation

```php
private function generatePdfReport(SuitabilityReportCustomer $customer): string
{
    $pdf = PDF::loadView('suitability-reports.pdf', [
        'customer' => $customer,
        'report' => $customer->report,
        'recommendations' => $customer->report->recommendation,
        'generated_at' => now(),
        'estate_planner' => $customer->estatePlanner
    ]);
    
    $filename = "suitability_report_{$customer->id}_" . now()->format('Y-m-d') . ".pdf";
    $path = storage_path("app/exports/{$filename}");
    
    $pdf->save($path);
    
    return $filename;
}
```

## Progress Tracking API

### Calculate Assessment Progress

```php
// Service method
public function calculateAssessmentProgress(SuitabilityReport $report): array
{
    $sections = [
        'personal_info' => [
            'fields' => ['relationship_status', 'partner_name'],
            'weight' => 20
        ],
        'children_info' => [
            'fields' => ['child_count', 'children_under_18', 'step_children', 'child_with_disability_or_care_needs'],
            'weight' => 25
        ],
        'assets' => [
            'fields' => ['property_count', 'owns_business', 'estate_value'],
            'weight' => 25
        ],
        'legal_status' => [
            'fields' => ['has_will', 'has_trust', 'has_lpa', 'partner_has_will', 'partner_has_lpa'],
            'weight' => 30
        ]
    ];
    
    $sectionProgress = [];
    $totalProgress = 0;
    
    foreach ($sections as $sectionName => $section) {
        $completedFields = 0;
        $totalFields = count($section['fields']);
        
        foreach ($section['fields'] as $field) {
            if (!is_null($report->$field)) {
                $completedFields++;
            }
        }
        
        $sectionPercentage = $totalFields > 0 ? ($completedFields / $totalFields) * 100 : 0;
        $weightedProgress = ($sectionPercentage * $section['weight']) / 100;
        
        $sectionProgress[$sectionName] = [
            'completed_fields' => $completedFields,
            'total_fields' => $totalFields,
            'percentage' => round($sectionPercentage),
            'weighted_contribution' => round($weightedProgress, 1)
        ];
        
        $totalProgress += $weightedProgress;
    }
    
    return [
        'total_progress' => round($totalProgress),
        'sections' => $sectionProgress,
        'is_complete' => $totalProgress >= 95, // Allow for some optional fields
        'next_section' => $this->getNextIncompleteSection($sectionProgress)
    ];
}
```

## Error Handling

### HubSpot Integration Errors

**Contact Not Found:**
```php
try {
    $contact = $hubSpotService->getContactById($hubspotId);
} catch (ApiException $e) {
    Log::error('HubSpot contact not found', [
        'hubspot_id' => $hubspotId,
        'error' => $e->getMessage()
    ]);
    
    abort(404, 'Contact not found in HubSpot');
}
```

**API Rate Limit:**
```php
try {
    $contact = $hubSpotService->getContactById($hubspotId);
} catch (RateLimitException $e) {
    Log::warning('HubSpot rate limit exceeded', [
        'hubspot_id' => $hubspotId,
        'retry_after' => $e->getRetryAfter()
    ]);
    
    // Implement exponential backoff
    return $this->retryAfterDelay($e->getRetryAfter());
}
```

### Validation Errors

**Assessment Data Validation:**
```json
{
  "error": "Validation failed",
  "errors": {
    "child_count": ["The child count must be between 0 and 20."],
    "estate_value": ["The selected estate value is invalid."],
    "marketing_preference": ["The selected marketing preference is invalid."]
  }
}
```

## Real-time Updates

### WebSocket Events

The domain broadcasts real-time updates during assessment:

**Assessment Progress:**
```json
{
  "event": "assessment.progress_updated",
  "customer_id": "01HN123ABC456DEF789",
  "progress": 75,
  "section": "legal_status",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Recommendations Generated:**
```json
{
  "event": "recommendations.generated",
  "customer_id": "01HN123ABC456DEF789",
  "recommendation_count": 3,
  "high_priority_count": 1,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Best Practices

### Assessment Design
- **Progressive Disclosure**: Show questions progressively to avoid overwhelming users
- **Auto-Save**: Implement frequent auto-saving to prevent data loss
- **Validation**: Provide real-time validation with helpful error messages
- **Progress Indicators**: Show clear progress throughout the assessment

### HubSpot Integration
- **Error Handling**: Implement robust error handling for API failures
- **Rate Limiting**: Respect HubSpot API rate limits
- **Data Mapping**: Ensure consistent data mapping between systems
- **Logging**: Log all integration activities for debugging

### Performance
- **Lazy Loading**: Load related data only when needed
- **Caching**: Cache frequently accessed data
- **Database Optimization**: Use appropriate indexes and query optimization
- **Background Processing**: Move heavy operations to background jobs
