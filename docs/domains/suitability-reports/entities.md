# Suitability Reports Domain - Entities

The Suitability Reports domain contains entities for customer management, assessment data, and recommendation generation.

## Core Models

### SuitabilityReportCustomer
**Location**: `App\Domains\SuitabilityReports\Models\SuitabilityReportCustomer`

The customer entity specifically for suitability assessments.

**Key Properties:**
```php
use HasUlids; // UUID-based primary keys

protected $fillable = [
    'name',              // Customer full name
    'estate_planner_id', // Assigned estate planner (User ID)
    'hubspot_id',        // HubSpot contact ID
    'nova_id',           // Nova system ID (if applicable)
];
```

**Automatic Behaviors:**
```php
public static function booted(): void
{
    // Automatically create an associated SuitabilityReport when a new customer is created
    self::created(function (self $customer): void {
        $customer->report()->create();
    });
}
```

**Relationships:**
```php
// Belongs to an estate planner (User)
public function estatePlanner(): BelongsTo
{
    return $this->belongsTo(User::class, 'estate_planner_id');
}

// Has one suitability report
public function report(): HasOne
{
    return $this->hasOne(SuitabilityReport::class, 'customer_id');
}
```

**Key Features:**
- **ULID Primary Keys**: Uses ULIDs for unique, sortable identifiers
- **Multi-Source Integration**: Supports HubSpot, Nova, and manual data entry
- **Automatic Report Creation**: Creates associated report on customer creation
- **Estate Planner Assignment**: Links customers to specific professionals

### SuitabilityReport
**Location**: `App\Domains\SuitabilityReports\Models\SuitabilityReport`

The comprehensive assessment entity containing all suitability data.

**Assessment Fields:**
```php
protected $fillable = [
    // Partner Information
    'partner_name',
    'relationship_status',
    
    // Children Information
    'child_count',
    'children_under_18',
    'step_children',
    'child_with_disability_or_care_needs',
    
    // Asset Information
    'property_count',
    'owns_business',
    'estate_value',
    
    // Current Legal Status
    'has_will',
    'has_trust',
    'has_lpa',
    'partner_has_will',
    'partner_has_lpa',
    
    // Preferences
    'marketing_preference',
    'notes',
    
    // Recommendation Flags
    'life_interest_trust',
    'married_with_children_life_trust',
    'children_under_18_will_trust',
    'step_family_life_trust',
    'child_with_disability_trust',
    'property_ownership_iht',
    'business_ownership_iht',
    'value_of_estate_iht',
    'partner_will',
    'partner_lpa',
];
```

**Casts:**
```php
protected $casts = [
    'created_at' => 'datetime',
    'updated_at' => 'datetime',
];
```

**Eager Loading:**
```php
protected $with = [
    'suitabilityReportCustomer', // Always load customer data
];
```

**Relationships:**
```php
// Belongs to a customer
public function suitabilityReportCustomer(): BelongsTo
{
    return $this->belongsTo(
        related: SuitabilityReportCustomer::class, 
        ownerKey: 'customer_id'
    );
}

// Has one recommendation
public function recommendation(): HasOne
{
    return $this->hasOne(SuitabilityReportRecommendation::class);
}
```

**Assessment Categories:**

#### Personal & Family Information
- **Relationship Status**: Current relationship status
- **Partner Details**: Partner name and information
- **Children**: Number of children, ages, special needs considerations
- **Step-Family**: Step-children and blended family dynamics

#### Asset Portfolio
- **Property**: Number of properties owned
- **Business Interests**: Business ownership status
- **Estate Value**: Total estimated estate value
- **Complexity Indicators**: Factors affecting estate complexity

#### Legal Document Status
- **Will Status**: Current will existence and currency
- **Trust Arrangements**: Existing trust structures
- **LPA Status**: Lasting Power of Attorney arrangements
- **Partner Legal Status**: Partner's legal document status

#### Recommendation Indicators
- **Trust Recommendations**: Various trust structure recommendations
- **IHT Planning**: Inheritance tax planning indicators
- **Legal Document Needs**: Required legal document updates

### SuitabilityReportRecommendation
**Location**: `App\Domains\SuitabilityReports\Models\SuitabilityReportRecommendation`

AI-generated recommendations based on assessment data.

**Recommendation Categories:**
```php
protected $fillable = [
    'will',   // Will-related recommendations
    'trusts', // Trust planning recommendations
    'lpa',    // Lasting Power of Attorney recommendations
    'iht',    // Inheritance Tax planning recommendations
];
```

**Casts:**
```php
protected $casts = [
    'created_at' => 'datetime',
    'updated_at' => 'datetime',
];
```

**Relationships:**
```php
// Belongs to a suitability report
public function report(): BelongsTo
{
    return $this->belongsTo(SuitabilityReport::class);
}
```

**Recommendation Structure:**
Each recommendation field contains structured data:
```php
// Example recommendation structure
[
    'priority' => 'high',
    'title' => 'Will Creation Required',
    'description' => 'Based on your assessment, you need a will...',
    'action_items' => [
        'Schedule will consultation',
        'Gather asset information',
        'Consider executor appointments'
    ],
    'estimated_cost' => '£500-£800',
    'timeframe' => '2-4 weeks'
]
```

## Data Transfer Objects (DTOs)

### Assessment DTOs
**Location**: `App\Domains\SuitabilityReports\DTOs\`

DTOs for handling assessment data transfer and validation.

#### AssessmentDataDTO
```php
final readonly class AssessmentDataDTO
{
    public function __construct(
        public ?string $partnerName,
        public ?string $relationshipStatus,
        public ?int $childCount,
        public ?bool $childrenUnder18,
        public ?bool $stepChildren,
        public ?bool $childWithDisabilityOrCareNeeds,
        public ?int $propertyCount,
        public ?bool $ownsBusiness,
        public ?string $estateValue,
        public ?bool $hasWill,
        public ?bool $hasTrust,
        public ?bool $hasLpa,
        public ?bool $partnerHasWill,
        public ?bool $partnerHasLpa,
        public ?string $marketingPreference,
        public ?string $notes,
    ) {}
}
```

## Enumerations

### Assessment Enums
**Location**: `App\Domains\SuitabilityReports\Enums\`

#### RelationshipStatus
```php
enum RelationshipStatus: string
{
    case SINGLE = 'single';
    case MARRIED = 'married';
    case CIVIL_PARTNERSHIP = 'civil_partnership';
    case DIVORCED = 'divorced';
    case WIDOWED = 'widowed';
    case SEPARATED = 'separated';
}
```

#### EstateValue
```php
enum EstateValue: string
{
    case UNDER_325K = 'under_325k';
    case BETWEEN_325K_650K = '325k_650k';
    case BETWEEN_650K_1M = '650k_1m';
    case BETWEEN_1M_2M = '1m_2m';
    case OVER_2M = 'over_2m';
}
```

#### MarketingPreference
```php
enum MarketingPreference: string
{
    case EMAIL = 'email';
    case PHONE = 'phone';
    case POST = 'post';
    case NO_MARKETING = 'no_marketing';
}
```

## Forms & Validation

### Livewire Forms
**Location**: `App\Domains\SuitabilityReports\Forms\`

#### SuitabilityAssessmentForm
Comprehensive form handling for suitability assessments:

```php
class SuitabilityAssessmentForm extends Form
{
    // Form properties matching SuitabilityReport fields
    public ?string $partner_name = null;
    public ?string $relationship_status = null;
    public ?int $child_count = null;
    // ... other properties
    
    public function rules(): array
    {
        return [
            'partner_name' => 'nullable|string|max:255',
            'relationship_status' => 'nullable|in:single,married,civil_partnership,divorced,widowed,separated',
            'child_count' => 'nullable|integer|min:0|max:20',
            'children_under_18' => 'nullable|boolean',
            'step_children' => 'nullable|boolean',
            'child_with_disability_or_care_needs' => 'nullable|boolean',
            'property_count' => 'nullable|integer|min:0|max:50',
            'owns_business' => 'nullable|boolean',
            'estate_value' => 'nullable|in:under_325k,325k_650k,650k_1m,1m_2m,over_2m',
            'has_will' => 'nullable|boolean',
            'has_trust' => 'nullable|boolean',
            'has_lpa' => 'nullable|boolean',
            'partner_has_will' => 'nullable|boolean',
            'partner_has_lpa' => 'nullable|boolean',
            'marketing_preference' => 'nullable|in:email,phone,post,no_marketing',
            'notes' => 'nullable|string|max:2000',
        ];
    }
}
```

## Controllers

### StartSuitabilityReportFromHubspotController
**Location**: `App\Domains\SuitabilityReports\Http\StartSuitabilityReportFromHubspotController`

Handles creation of suitability reports from HubSpot contacts.

**Key Method:**
```php
public function __invoke(
    Request $request, 
    HubSpotApiService $hubSpotApiService, 
    string $hubspotId
) {
    $user = $request->user();

    try {
        $customer = SuitabilityReportCustomer::where('hubspot_id', $hubspotId)
            ->firstOr(function () use ($user, $hubSpotApiService, $hubspotId) {
                $contact = $hubSpotApiService->getContactById($hubspotId)->getProperties();

                $firstName = $contact['firstname'] ?? '';
                $lastName = $contact['lastname'] ?? '';
                $fullName = "{$firstName} {$lastName}";
                $email = $contact['email'] ?? '';

                return SuitabilityReportCustomer::create([
                    'hubspot_id' => $hubspotId,
                    'name' => trim($fullName) !== '' ? $fullName : $email,
                    'estate_planner_id' => $user->id,
                ]);
            });

        return redirect()->route('suitability.show', ['customer' => $customer->id]);
    } catch (ApiException $e) {
        Log::error('Failed to fetch HubSpot contact', [
            'hubspot_id' => $hubspotId, 
            'exception' => $e->getMessage()
        ]);
        abort(404);
    }
}
```

## Database Relationships

```mermaid
erDiagram
    SUITABILITY_REPORT_CUSTOMER {
        id string PK
        name string
        estate_planner_id bigint FK
        hubspot_id string
        nova_id string
        created_at timestamp
        updated_at timestamp
    }
    
    SUITABILITY_REPORT {
        id bigint PK
        customer_id string FK
        partner_name string
        relationship_status string
        child_count integer
        children_under_18 boolean
        step_children boolean
        child_with_disability_or_care_needs boolean
        property_count integer
        owns_business boolean
        estate_value string
        has_will boolean
        has_trust boolean
        has_lpa boolean
        partner_has_will boolean
        partner_has_lpa boolean
        marketing_preference string
        notes text
        life_interest_trust boolean
        married_with_children_life_trust boolean
        children_under_18_will_trust boolean
        step_family_life_trust boolean
        child_with_disability_trust boolean
        property_ownership_iht boolean
        business_ownership_iht boolean
        value_of_estate_iht boolean
        partner_will boolean
        partner_lpa boolean
        created_at timestamp
        updated_at timestamp
    }
    
    SUITABILITY_REPORT_RECOMMENDATION {
        id bigint PK
        suitability_report_id bigint FK
        will text
        trusts text
        lpa text
        iht text
        created_at timestamp
        updated_at timestamp
    }
    
    USER {
        id bigint PK
        name string
        email string
        created_at timestamp
        updated_at timestamp
    }
    
    SUITABILITY_REPORT_CUSTOMER ||--|| SUITABILITY_REPORT : "has one"
    SUITABILITY_REPORT ||--|| SUITABILITY_REPORT_RECOMMENDATION : "has one"
    USER ||--o{ SUITABILITY_REPORT_CUSTOMER : "estate planner"
```

## Business Logic

### Assessment Completion Logic
```php
public function isAssessmentComplete(): bool
{
    $requiredFields = [
        'relationship_status',
        'child_count',
        'property_count',
        'estate_value',
        'has_will',
        'has_lpa'
    ];
    
    foreach ($requiredFields as $field) {
        if (is_null($this->$field)) {
            return false;
        }
    }
    
    return true;
}
```

### Recommendation Generation Logic
```php
public function generateRecommendations(): array
{
    $recommendations = [];
    
    // Will recommendations
    if (!$this->has_will) {
        $recommendations['will'] = $this->generateWillRecommendation();
    }
    
    // Trust recommendations
    if ($this->shouldRecommendTrust()) {
        $recommendations['trusts'] = $this->generateTrustRecommendation();
    }
    
    // LPA recommendations
    if (!$this->has_lpa) {
        $recommendations['lpa'] = $this->generateLpaRecommendation();
    }
    
    // IHT recommendations
    if ($this->requiresIhtPlanning()) {
        $recommendations['iht'] = $this->generateIhtRecommendation();
    }
    
    return $recommendations;
}
```

## Performance Considerations

### Database Optimization
- **ULID Indexing**: Efficient indexing on ULID primary keys
- **Foreign Key Indexes**: Proper indexing on relationship fields
- **Eager Loading**: Strategic eager loading of related models
- **Query Optimization**: Optimized queries for assessment data

### HubSpot Integration
- **API Caching**: Caching of HubSpot contact data
- **Rate Limiting**: Proper handling of API rate limits
- **Error Recovery**: Automatic retry logic for failed API calls
- **Data Minimization**: Only fetch necessary contact data
