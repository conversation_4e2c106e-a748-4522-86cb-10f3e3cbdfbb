# Suitability Reports Domain

The Suitability Reports domain manages customer suitability assessments for legal services, providing comprehensive questionnaire systems, personalized recommendations, and seamless integration with HubSpot CRM for customer data management.

## Purpose

The Suitability Reports domain handles:
- **Customer Assessment**: Comprehensive suitability questionnaires for legal services
- **Recommendation Engine**: AI-powered service recommendations based on customer profiles
- **HubSpot Integration**: Seamless customer data synchronization and management
- **Estate Planning**: Specialized assessments for estate planning services
- **Report Generation**: Detailed suitability reports with recommendations
- **Customer Management**: Dedicated customer profiles for suitability assessments

## Key Features

### Customer Profile Management
- **Detailed Profiles**: Comprehensive customer information and preferences
- **HubSpot Sync**: Automatic synchronization with HubSpot contact data
- **Estate Planner Assignment**: Link customers to specific estate planners
- **Multi-Source Data**: Support for HubSpot, Nova, and manual data entry
- **Unique Identification**: ULID-based customer identification system

### Suitability Assessment
- **Comprehensive Questionnaire**: Detailed assessment covering all aspects of estate planning
- **Dynamic Forms**: Conditional questions based on previous answers
- **Progress Tracking**: Save and resume assessment progress
- **Validation**: Comprehensive validation of assessment responses
- **Auto-Save**: Automatic saving of assessment progress

### Recommendation System
- **AI-Powered Analysis**: Intelligent analysis of customer responses
- **Service Recommendations**: Personalized recommendations for legal services
- **Priority Scoring**: Recommendations ranked by importance and urgency
- **Detailed Explanations**: Clear explanations for each recommendation
- **Action Items**: Specific next steps for customers and estate planners

### Report Generation
- **Comprehensive Reports**: Detailed suitability reports with all assessments
- **Professional Formatting**: Clean, professional report layouts
- **Export Options**: PDF and other format exports
- **Customizable Templates**: Configurable report templates
- **Branding**: Branded reports with company information

## Architecture

```mermaid
graph TB
    subgraph "Suitability Reports Domain"
        subgraph "Models"
            Customer[Suitability Report Customer]
            Report[Suitability Report]
            Recommendation[Suitability Report Recommendation]
        end
        
        subgraph "Services"
            Assessment[Assessment Service]
            RecommendationEngine[Recommendation Engine]
            ReportGenerator[Report Generator]
        end
        
        subgraph "Controllers"
            HubSpotController[HubSpot Integration Controller]
            ReportController[Report Controller]
        end
        
        subgraph "Forms & DTOs"
            Forms[Livewire Forms]
            DTOs[Data Transfer Objects]
            Enums[Assessment Enums]
        end
    end
    
    subgraph "External Services"
        HubSpotCRM[HubSpot CRM]
        NovaSystem[Nova System]
        AI[AI Services]
    end
    
    subgraph "Global Domain"
        HubSpotService[HubSpot API Service]
    end
    
    Customer --> Report
    Report --> Recommendation
    
    HubSpotController --> Customer
    HubSpotService --> HubSpotController
    HubSpotCRM --> HubSpotService
    
    Assessment --> Report
    RecommendationEngine --> Recommendation
    AI --> RecommendationEngine
```

## Core Entities

### SuitabilityReportCustomer
The customer entity for suitability assessments:
- **Identity Management**: ULID-based unique identification
- **Multi-Source Integration**: HubSpot, Nova, and manual data support
- **Estate Planner Assignment**: Links to specific estate planning professionals
- **Automatic Report Creation**: Auto-creates associated suitability report
- **Relationship Management**: Links to User model for estate planner details

### SuitabilityReport
The comprehensive assessment entity:
- **Detailed Assessment**: 30+ fields covering all aspects of estate planning
- **Family Information**: Relationship status, children, step-children details
- **Asset Information**: Property, business ownership, estate value
- **Legal Status**: Current will, trust, and LPA status
- **Recommendations**: Links to generated recommendations
- **Progress Tracking**: Assessment completion status

### SuitabilityReportRecommendation
AI-generated recommendations:
- **Service Categories**: Will, trusts, LPA, and IHT recommendations
- **Detailed Analysis**: Specific recommendations for each service area
- **Priority Ranking**: Recommendations ranked by importance
- **Action Items**: Clear next steps for customers and professionals

## Assessment Categories

### Personal Information
- **Relationship Status**: Married, single, divorced, widowed
- **Partner Details**: Partner name and information
- **Children Information**: Number of children, ages, special needs
- **Step-Family Considerations**: Step-children and blended family dynamics

### Asset Assessment
- **Property Portfolio**: Number and types of properties owned
- **Business Interests**: Business ownership and structure
- **Estate Valuation**: Total estate value assessment
- **Asset Complexity**: Complex asset structures and considerations

### Legal Status Review
- **Current Will**: Existence and currency of will
- **Trust Arrangements**: Current trust structures
- **Lasting Power of Attorney**: LPA status for property/financial and health/welfare
- **Partner Legal Status**: Partner's legal document status

### Recommendation Areas
- **Will Services**: Will creation, updates, and reviews
- **Trust Planning**: Various trust structures and recommendations
- **LPA Services**: Power of attorney arrangements
- **Inheritance Tax Planning**: IHT mitigation strategies

## HubSpot Integration

### Customer Data Sync
- **Contact Retrieval**: Automatic fetching of HubSpot contact data
- **Data Mapping**: Mapping HubSpot fields to customer profile
- **Real-time Updates**: Synchronization of customer information
- **Error Handling**: Robust error handling for API failures

### Workflow Integration
- **Direct Links**: Direct links from HubSpot to suitability assessments
- **Status Updates**: Assessment status updates back to HubSpot
- **Activity Tracking**: Assessment activities logged in HubSpot
- **Lead Qualification**: Assessment results inform lead qualification

## Web Interface

### Assessment Flow
- **Progressive Disclosure**: Questions revealed progressively
- **Conditional Logic**: Questions adapt based on previous answers
- **Progress Indicators**: Clear progress tracking throughout assessment
- **Save & Resume**: Ability to save progress and resume later
- **Validation**: Real-time validation with helpful error messages

### Results Display
- **Comprehensive Overview**: Complete assessment results display
- **Recommendation Sections**: Organized recommendation display
- **Action Items**: Clear next steps and action items
- **Export Options**: PDF export and sharing capabilities

## Business Logic

### Assessment Logic
- **Conditional Questions**: Questions appear based on previous answers
- **Validation Rules**: Comprehensive validation for all inputs
- **Progress Calculation**: Automatic progress tracking
- **Completion Detection**: Automatic detection of assessment completion

### Recommendation Engine
- **Rule-Based Logic**: Recommendations based on assessment responses
- **Priority Scoring**: Recommendations ranked by importance and urgency
- **Personalization**: Recommendations tailored to specific customer situations
- **Explanation Generation**: Clear explanations for each recommendation

## Data Flow

### Customer Creation Flow
1. **HubSpot Link**: User clicks link from HubSpot contact
2. **Customer Lookup**: System checks for existing customer record
3. **Data Retrieval**: HubSpot contact data retrieved if new customer
4. **Customer Creation**: New customer record created with HubSpot data
5. **Report Initialization**: Empty suitability report automatically created
6. **Assessment Start**: User redirected to assessment interface

### Assessment Flow
1. **Form Display**: Assessment form displayed with current progress
2. **Progressive Completion**: User completes assessment sections
3. **Auto-Save**: Progress automatically saved
4. **Validation**: Real-time validation of responses
5. **Completion**: Assessment marked as complete
6. **Recommendation Generation**: AI-powered recommendations generated

## Configuration

### Assessment Questions
- **Question Configuration**: Configurable assessment questions
- **Conditional Logic**: Question dependencies and conditions
- **Validation Rules**: Input validation and requirements
- **Help Text**: Contextual help and explanations

### Recommendation Rules
- **Rule Configuration**: Configurable recommendation rules
- **Priority Weights**: Importance weighting for different factors
- **Template Management**: Recommendation text templates
- **Threshold Settings**: Recommendation trigger thresholds

## Security & Privacy

### Data Protection
- **Secure Storage**: Customer data stored with appropriate security
- **Access Control**: Role-based access to customer information
- **Audit Logging**: Comprehensive logging of all data access
- **Data Retention**: Configurable data retention policies

### HubSpot Integration Security
- **API Authentication**: Secure API authentication with HubSpot
- **Data Minimization**: Only necessary data retrieved from HubSpot
- **Error Handling**: Secure error handling without data exposure
- **Rate Limiting**: Appropriate rate limiting for API calls

## Performance Considerations

### Database Optimization
- **Efficient Queries**: Optimized database queries for customer data
- **Relationship Loading**: Eager loading of related data
- **Indexing**: Appropriate database indexes for performance
- **Caching**: Strategic caching of frequently accessed data

### HubSpot API Optimization
- **Request Batching**: Batching of API requests where possible
- **Caching**: Caching of HubSpot data to reduce API calls
- **Error Recovery**: Automatic retry logic for failed API calls
- **Rate Limit Handling**: Proper handling of API rate limits

## Next Steps

- [Entities Documentation](./entities.md) - Detailed model information
- [API Reference](./api.md) - Available endpoints and integration
- [Leads Domain](../leads/) - Lead management functionality
- [Radar Domain](../radar/) - Document analysis features
