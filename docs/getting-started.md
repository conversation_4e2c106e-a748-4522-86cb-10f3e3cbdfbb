# Getting Started

Welcome to the <PERSON>llie application documentation! This guide will help you get up and running with the application.

## Overview

Ollie is a Laravel-based application designed for customer management with unique reference number generation and comprehensive API endpoints.

## Key Features

- **Customer Management**: Create and manage customer records with unique reference numbers
- **Reference Number System**: Atomic, unique customer reference numbers following the format `OL-XXX-XXX-XXX`
- **RESTful API**: Standard REST endpoints for customer operations
- **Search Functionality**: Exact match customer search with automatic creation
- **Data Import/Export**: CSV import and export capabilities

## Quick Start

### Prerequisites

- PHP 8.1 or higher
- Composer
- Node.js and npm
- Database (PostgreSQL recommended)

### Installation

1. Clone the repository
2. Install PHP dependencies: `composer install`
3. Install Node.js dependencies: `npm install`
4. Copy environment file: `cp .env.example .env`
5. Generate application key: `php artisan key:generate`
6. Run migrations: `php artisan migrate`
7. Start the development server: `php artisan serve`

### Development

- Run Laravel development server: `php artisan serve`
- Run Vite for asset compilation: `npm run dev`
- Run tests: `php artisan test`

## Next Steps

- [Installation Guide](./installation.md)
- [API Reference](./api/)
- [Customer Management](./api/customers.md)
