# Ollie Application Overview

Ollie is a comprehensive Laravel-based application designed for legal services, providing intelligent document analysis, lead management, customer assessment tools, and seamless CRM integration.

## Application Architecture

Ollie follows Domain-Driven Design (DDD) principles, organizing business logic into four distinct domains:

```mermaid
graph TB
    subgraph "Ollie Application"
        subgraph "Global Domain"
            HubSpot[HubSpot Integration]
            Utils[Shared Utilities]
            Config[Configuration]
        end

        subgraph "Leads Domain"
            LeadMgmt[Lead Management]
            DealTrack[Deal Tracking]
            SLA[SLA Management]
        end

        subgraph "Radar Domain"
            DocAnalysis[Document Analysis]
            OCR[OCR Processing]
            AIScoring[AI Scoring]
        end

        subgraph "Suitability Domain"
            Assessment[Customer Assessment]
            Recommendations[AI Recommendations]
            Reports[Report Generation]
        end
    end

    subgraph "External Services"
        HubSpotCRM[HubSpot CRM]
        OpenAI[OpenAI API]
        Storage[File Storage]
    end

    HubSpot --> HubSpotCRM
    LeadMgmt --> HubSpot
    Assessment --> HubSpot
    DocAnalysis --> OpenAI
    AIScoring --> OpenAI
    Recommendations --> OpenAI
    DocAnalysis --> Storage
```

## Core Capabilities

### 🎯 Lead Management & Sales Pipeline
- **HubSpot Integration**: Real-time synchronization with HubSpot CRM
- **Deal Tracking**: Complete deal lifecycle management through sales stages
- **SLA Management**: Automated call scheduling with breach detection
- **Lead Source Configuration**: Customizable lead sources with specific SLA rules
- **Performance Analytics**: Lead source performance tracking and optimization

### 📄 Intelligent Document Analysis
- **OCR Processing**: Advanced text extraction from legal documents
- **AI-Powered Analysis**: OpenAI integration for document understanding
- **Scoring System**: Rule-based quality assessment and scoring
- **Tag Extraction**: Automated categorization and metadata extraction
- **Insight Generation**: AI-generated insights and recommendations

### 📋 Customer Suitability Assessment
- **Comprehensive Questionnaires**: Detailed assessment forms for legal services
- **Progressive Disclosure**: Smart form flow with conditional questions
- **AI Recommendations**: Personalized service recommendations based on responses
- **Report Generation**: Professional suitability reports with actionable insights
- **Multi-Source Integration**: HubSpot, Nova, and manual data entry support

### 🔧 Shared Infrastructure
- **HubSpot API Service**: Centralized HubSpot integration layer
- **Configuration Management**: Environment-specific settings and validation
- **Error Handling**: Comprehensive error tracking and recovery
- **Queue System**: Background job processing for heavy operations

## Technology Stack

### Backend
- **Laravel 11**: Modern PHP framework with latest features
- **PHP 8.1+**: Latest PHP version with type declarations
- **PostgreSQL**: Primary database for data persistence
- **Redis**: Caching and queue management
- **Laravel Horizon**: Queue monitoring and management

### Frontend
- **Livewire**: Dynamic, reactive components
- **Alpine.js**: Lightweight JavaScript framework
- **Tailwind CSS**: Utility-first CSS framework
- **Vite**: Modern build tool and asset compilation

### External Integrations
- **HubSpot CRM API**: Customer and deal management
- **OpenAI API**: AI-powered analysis and insights
- **Laravel Excel**: Data import/export capabilities
- **Sentry**: Error tracking and monitoring

### Development Tools
- **Pest**: Modern PHP testing framework
- **Laravel Pint**: Code style fixing
- **Husky**: Git hooks for code quality
- **VitePress**: Documentation framework

## Domain Deep Dive

### [Global Domain](./domains/global/)
**Shared Services & Foundation**

The Global domain provides the foundational layer that other domains depend on:
- **HubSpot API Service**: Centralized HubSpot integration
- **Shared Enumerations**: Common enums with utility methods
- **Configuration Management**: Environment and service configuration
- **Error Handling Patterns**: Consistent error handling across domains

### [Leads Domain](./domains/leads/)
**Lead Lifecycle Management**

Manages the complete lead journey from capture to conversion:
- **Deal Management**: Track deals through customizable sales stages
- **SLA Calculations**: Business-hour aware call scheduling
- **HubSpot Webhooks**: Real-time event processing from HubSpot
- **Lead Source Analytics**: Performance tracking and optimization

### [Radar Domain](./domains/radar/)
**AI-Powered Document Analysis**

Provides intelligent analysis of legal documents:
- **Document Processing Pipeline**: OCR → AI Analysis → Scoring → Insights
- **Rule-Based Scoring**: Configurable scoring rules for document quality
- **Tag Extraction**: Automated categorization and metadata extraction
- **Export Capabilities**: Multi-sheet Excel exports for analysis

### [Suitability Reports Domain](./domains/suitability-reports/)
**Customer Assessment & Recommendations**

Comprehensive customer suitability assessment system:
- **Progressive Assessment Forms**: Smart questionnaires with conditional logic
- **AI Recommendation Engine**: Personalized service recommendations
- **HubSpot Customer Sync**: Seamless customer data integration
- **Professional Reports**: Branded PDF reports with recommendations

## Key Features

### Real-Time Integration
- **HubSpot Webhooks**: Instant synchronization of deals and contacts
- **Live Form Updates**: Real-time form validation and auto-save
- **Progress Tracking**: Live progress indicators for assessments
- **Status Updates**: Real-time processing status for document analysis

### AI-Powered Intelligence
- **Document Understanding**: AI analysis of legal document content
- **Quality Scoring**: Intelligent scoring based on multiple criteria
- **Personalized Recommendations**: Tailored service recommendations
- **Insight Generation**: Automated insights and action items

### Business Process Automation
- **SLA Management**: Automated call scheduling and breach detection
- **Report Generation**: Automatic creation of professional reports
- **Data Synchronization**: Seamless data flow between systems
- **Background Processing**: Asynchronous processing for heavy operations

### User Experience
- **Progressive Disclosure**: Information revealed as needed
- **Responsive Design**: Works across all device types
- **Intuitive Navigation**: Clear, logical information architecture
- **Performance Optimized**: Fast loading and responsive interactions

## Getting Started

### Quick Navigation
- **[Getting Started Guide](./getting-started.md)** - Set up and start using Ollie
- **[Domain Architecture](./domains/)** - Understand the application structure
- **[API Documentation](./api/)** - Explore available APIs and endpoints
- **[Installation Guide](./installation.md)** - Development environment setup

### For Developers
1. **[Installation Guide](./installation.md)** - Set up the development environment
2. **[Domain Architecture](./domains/)** - Understand the domain structure
3. **[API Documentation](./api/)** - Explore available APIs and endpoints
4. **Testing Guide** - Learn about testing strategies and tools

### For Users
1. **User Guide** - Learn how to use the application
2. **Lead Management** - Managing leads and deals
3. **Document Analysis** - Analyzing documents with Radar
4. **Assessments** - Creating suitability assessments

## Support & Resources

### Documentation
- **[API Reference](./api/)** - Complete API documentation
- **[Domain Guides](./domains/)** - Detailed domain documentation
- **Troubleshooting** - Common issues and solutions
- **FAQ** - Frequently asked questions

### Development
- **Contributing Guide** - How to contribute to the project
- **Code Standards** - Coding standards and best practices
- **Testing Guidelines** - Testing strategies and requirements
- **Deployment Guide** - Production deployment instructions

---

*This overview provides a high-level understanding of the Ollie application. For detailed information about specific domains, features, or APIs, please explore the relevant sections of this documentation.*
