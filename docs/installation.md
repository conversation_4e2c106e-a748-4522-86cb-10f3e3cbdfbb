# Installation

This guide provides detailed installation instructions for the Ollie application.

## System Requirements

- **PHP**: 8.1 or higher
- **Composer**: Latest version
- **Node.js**: 18.x or higher
- **npm**: 9.x or higher
- **Database**: PostgreSQL 13+ (recommended) or MySQL 8.0+
- **Redis**: For caching and queues (optional but recommended)

## Step-by-Step Installation

### 1. Clone the Repository

```bash
git clone https://github.com/your-org/ollie.git
cd ollie
```

### 2. Install PHP Dependencies

```bash
composer install
```

### 3. Install Node.js Dependencies

```bash
npm install
```

### 4. Environment Configuration

```bash
# Copy the example environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

Edit the `.env` file with your database and other configuration settings:

```env
DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=ollie
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 5. Database Setup

```bash
# Run database migrations
php artisan migrate

# (Optional) Seed the database with sample data
php artisan db:seed
```

### 6. Build Assets

```bash
# For development
npm run dev

# For production
npm run build
```

### 7. Start the Application

```bash
# Start the Laravel development server
php artisan serve

# The application will be available at http://localhost:8000
```

## Docker Setup (Alternative)

If you prefer using Docker:

```bash
# Start the application with Docker Compose
docker-compose up -d

# Run migrations inside the container
docker-compose exec app php artisan migrate
```

## Verification

To verify your installation:

1. Visit `http://localhost:8000` in your browser
2. Check that the application loads without errors
3. Run the test suite: `php artisan test`

## Troubleshooting

### Common Issues

- **Permission errors**: Ensure storage and cache directories are writable
- **Database connection**: Verify database credentials and server status
- **Asset compilation**: Clear cache with `php artisan cache:clear`

### Getting Help

If you encounter issues during installation, please check the troubleshooting section or contact the development team.
