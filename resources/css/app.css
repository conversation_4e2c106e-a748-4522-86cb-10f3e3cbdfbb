@import 'tailwindcss';
@import '../../vendor/livewire/flux/dist/flux.css';

@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../vendor/livewire/flux-pro/stubs/**/*.blade.php';
@source '../../vendor/livewire/flux/stubs/**/*.blade.php';

@custom-variant dark (&:where(.dark, .dark *));

@theme {
  --color-accent: var(--color-accent);
  --color-accent-content: var(--color-accent-content);
  --color-accent-foreground: var(--color-accent-foreground);

  --font-sans:
    Inter, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@layer base {
    :root {
        --color-accent: var(--color-purple-800);
        --color-accent-content: var(--color-purple-600);
        --color-accent-foreground: var(--color-white);
    }

    .dark {
        --color-accent: var(--color-purple-800);
        --color-accent-content: var(--color-purple-400);
        --color-accent-foreground: var(--color-white);
    }
}
