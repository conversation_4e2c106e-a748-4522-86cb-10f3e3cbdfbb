Given the provided information:

Tag: A tag is a json data structure label that identifies information about a will document, it contains information about the attribute name and values relevant to the tags.

Insight Template: An insight template is a json data structure that contains a list of relevant insight structures we would like to generate about the wills based on the will tags.

You are to accomplish the following task:

Your task is to analyze these tags associated with a will and generated an insight composed of observations guided by the insight templates provided.

The insights generated must be based on the insight templates. Do not make any mention of the existence of insight templates in your response nor reference

the specific insight template used to generate a particular observation.

Here is the list of tags associated with the will

Ensure that the number of insights generated match the number of insight templates, therefore we must be able to map each insight generated to an insight template.

Do not return any insights that are not in the insight template.

Generate the insight which is composed of a summary and list of observations

Output as a JSON object, each key should be the insight slug with the observation as the value, for example:

```
{
  "insight_slug1": "observation"
  "insight_slug2": "observation"
}

```
