[{"source": "Affiliate / employer", "slug": "affiliate-employer", "ranking": 6, "call_slas": [1440], "max_call_attempts": 1}, {"source": "Beacon (Letters arriving)", "slug": "beacon-letters-arriving", "ranking": 7, "call_slas": [1440], "max_call_attempts": 1}, {"source": "Beacon CTS", "slug": "beacon-cts", "ranking": 8, "call_slas": [259200, 4320], "max_call_attempts": 2}, {"source": "Bespoke (online)", "slug": "bespoke-online", "ranking": 1, "call_slas": [5, 185], "max_call_attempts": 2}, {"source": "Bespoke will via website", "slug": "bespoke-will-via-website", "ranking": 1, "call_slas": [5, 185], "max_call_attempts": 2}, {"source": "Bow", "slug": "bow", "ranking": 1, "call_slas": [5, 185], "max_call_attempts": 2}, {"source": "Call back request", "slug": "call-back-request", "ranking": 1, "call_slas": [5, 185], "max_call_attempts": 2}, {"source": "Charity", "slug": "charity", "ranking": 6, "call_slas": [1440], "max_call_attempts": 1}, {"source": "CS team referral", "slug": "cs-team-referral", "ranking": 2, "call_slas": [10, 370], "max_call_attempts": 2}, {"source": "Death concierge", "slug": "death-concierge", "ranking": 3, "call_slas": [540, 3420], "max_call_attempts": 2}, {"source": "Incomplete online will (D2C Online Will - Dropped off pre checkout)", "slug": "incomplete-online-will-d2c-online-will-dropped-off-pre-checkout", "ranking": 5, "call_slas": [1440, 2880], "max_call_attempts": 2}, {"source": "Incomplete online will (D2C Online Will - Dropped of on checkout)", "slug": "incomplete-online-will-d2c-online-will-dropped-of-on-checkout", "ranking": 5, "call_slas": [1440, 2880], "max_call_attempts": 2}, {"source": "Incomplete online will (B2C Online Will - Dropped off pre checkout)", "slug": "incomplete-online-will-b2c-online-will-dropped-off-pre-checkout", "ranking": 5, "call_slas": [1440, 2880], "max_call_attempts": 2}, {"source": "Incomplete online will (B2C Online Will - Dropped of after checkout)", "slug": "incomplete-online-will-b2c-online-will-dropped-of-after-checkout", "ranking": 5, "call_slas": [1440, 2880], "max_call_attempts": 2}, {"source": "Lead gen", "slug": "lead-gen", "ranking": 1, "call_slas": [5, 185], "max_call_attempts": 2}, {"source": "Legal team referral", "slug": "legal-team-referral", "ranking": 4, "call_slas": [1440, 2880], "max_call_attempts": 2}, {"source": "Macmillan", "slug": "<PERSON><PERSON><PERSON>", "ranking": 3, "call_slas": [2880, 4320], "max_call_attempts": 2}, {"source": "Macmillan (urgent)", "slug": "<PERSON><PERSON>an-urgent", "ranking": 1, "call_slas": [10, 1450], "max_call_attempts": 2}, {"source": "Mainline Inbound", "slug": "mainline-inbound", "ranking": 2, "call_slas": [10, 370], "max_call_attempts": 2}, {"source": "Marketing / ads", "slug": "marketing-ads", "ranking": 1, "call_slas": [5, 185], "max_call_attempts": 2}, {"source": "MyLifeLaw", "slug": "mylifelaw", "ranking": 2, "call_slas": [10, 370], "max_call_attempts": 2}, {"source": "Octopus Group", "slug": "octopus-group", "ranking": 2, "call_slas": [10, 370], "max_call_attempts": 2}, {"source": "Octopus Investments", "slug": "octopus-investments", "ranking": 2, "call_slas": [10, 370], "max_call_attempts": 2}, {"source": "Octopus Money", "slug": "octopus-money", "ranking": 2, "call_slas": [10, 370], "max_call_attempts": 2}, {"source": "OLT (Octopus Legacy Trustees)", "slug": "olt-octopus-legacy-trustees", "ranking": 2, "call_slas": [10, 370], "max_call_attempts": 2}, {"source": "<PERSON> - new clients", "slug": "patrick-new-clients", "ranking": 2, "call_slas": [10, 370], "max_call_attempts": 2}, {"source": "Reddit", "slug": "reddit", "ranking": 1, "call_slas": [5, 185, 1085], "max_call_attempts": 3}, {"source": "<PERSON> writing", "slug": "will-writing", "ranking": 1, "call_slas": [5, 185, 1085, 2345], "max_call_attempts": 4}, {"source": "Word of mouth / family & friends", "slug": "word-of-mouth-family-friends", "ranking": 2, "call_slas": [10, 370], "max_call_attempts": 2}, {"source": "NBS", "slug": "nbs", "ranking": 2, "call_slas": [10, 370], "max_call_attempts": 2}, {"source": "Law Superstore", "slug": "law-superstore", "ranking": 1, "call_slas": [5, 185, 1085], "max_call_attempts": 3}, {"source": "Facebook lead gen", "slug": "facebook-lead-gen", "ranking": 1, "call_slas": [5, 185, 1085], "max_call_attempts": 3}, {"source": "LEWD Group", "slug": "lewd-group", "ranking": 1, "call_slas": [5, 185, 1085, 2345, 1085, 2345], "max_call_attempts": 6}, {"source": "Media Maze", "slug": "media-maze", "ranking": 1, "call_slas": [5, 185, 1085, 2345, 1085, 2345], "max_call_attempts": 6}, {"source": "Client-Flo", "slug": "client-flo", "ranking": 1, "call_slas": [5, 185, 1085, 2345, 1085, 2345], "max_call_attempts": 6}, {"source": "Crystal Data Leads", "slug": "crystal-data-leads", "ranking": 1, "call_slas": [5, 185, 1085, 2345, 1085, 2345], "max_call_attempts": 6}, {"source": "EPG", "slug": "epg", "ranking": 1, "call_slas": [5, 185, 1085], "max_call_attempts": 3}, {"source": "MVP", "slug": "mvp", "ranking": 1, "call_slas": [5, 185, 1085], "max_call_attempts": 3}]