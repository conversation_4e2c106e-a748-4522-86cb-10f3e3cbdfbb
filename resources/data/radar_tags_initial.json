[{"name": "Appointed_Executors_and_Trustees", "description": "Captures the names of individuals appointed as executors and trustees in a will. executors are responsible for managing the estate according to the will's provisions, while trustees manage any trusts established within the will. this information is crucial for understanding who will oversee the distribution of assets and ensure the testator's wishes are fulfilled.", "type": "Open-ended", "outputConstraint": null, "example": "", "possibleValues": "Open-ended", "significance": "LOW"}, {"name": "Key_Provisions_of_the_Will", "description": "Identifies the significant clauses and stipulations outlined in a will. key provisions may include specific bequests, conditions for inheritance, and instructions for the distribution of the estate. understanding these provisions is essential for interpreting the testator's intentions and the legal implications for beneficiaries.", "type": "Open-ended", "outputConstraint": null, "example": "", "possibleValues": "Open-ended", "significance": "LOW"}, {"name": "Beneficiaries", "description": "Lists the individuals or entities designated to receive assets from the estate. beneficiaries can include family members, friends, charities, or organizations. identifying beneficiaries is vital for understanding who stands to gain from the estate and under what conditions. Please specify if a beneficiary is a primary beneficiary or secondary, tertiary and so on.", "type": "Open-ended", "outputConstraint": null, "example": "", "possibleValues": "Open-ended", "significance": "LOW"}, {"name": "Residuary_Estate_Distribution", "description": "Details regarding how the remaining assets of the estate, after specific bequests and debts have been settled, are to be divided among the beneficiaries.", "type": "Open-ended", "outputConstraint": "equally,proportionally,specific shares,to children", "example": "", "possibleValues": "Open-ended", "significance": "LOW"}, {"name": "Conditions_for_Inheritance", "description": "Captures any specific conditions or requirements that beneficiaries must meet to receive their inheritance. conditions may include survival clauses, age restrictions, or stipulations regarding the beneficiary's behavior. understanding these conditions is crucial for determining the eligibility of beneficiaries.", "type": "Open-ended", "outputConstraint": null, "example": "", "possibleValues": "Open-ended", "significance": "LOW"}, {"name": "Funeral_Arrangements", "description": "Instructions provided by the testator regarding their funeral and burial wishes, which may include specific requests for cremation, burial, or other arrangements.", "type": "Open-ended", "outputConstraint": "cremation,burial,no specific arrangements,donation of body", "example": "", "possibleValues": "Open-ended", "significance": "LOW"}, {"name": "Specific_Bequests", "description": "Items or amounts specifically designated to individuals in the will, which may include personal property, monetary gifts, or other assets.", "type": "Open-ended", "outputConstraint": "jewelry,real estate,monetary gifts,family heirlooms", "example": "", "possibleValues": "Open-ended", "significance": "LOW"}, {"name": "Trustee_Powers", "description": "The specific powers granted to trustees in managing the estate, including investment decisions, distribution of assets, and other fiduciary responsibilities.", "type": "Open-ended", "outputConstraint": "investment management,asset distribution,debt settlement,property management", "example": "", "possibleValues": "Open-ended", "significance": "HIGH"}, {"name": "Implications_of_Standard_Provisions", "description": "Addresses the implications of standard legal provisions that may be included in a will, such as those from the society of trust and estate practitioners. these provisions can affect the administration of the estate and the rights of beneficiaries. understanding these implications is crucial for navigating the legal landscape of estate management.", "type": "Open-ended", "outputConstraint": null, "example": "", "possibleValues": "Open-ended", "significance": "LOW"}, {"name": "Survival_Clauses", "description": "Identifies any clauses that stipulate conditions related to the survival of beneficiaries or other parties involved in the will. survival clauses may dictate that a beneficiary must outlive the testator by a certain period to inherit. these clauses are important for determining the validity of bequests under specific circumstances.", "type": "Open-ended", "outputConstraint": null, "example": "", "possibleValues": "Open-ended", "significance": "LOW"}, {"name": "Distribution_of_Estate", "description": "Outlines the process and methodology for distributing the estate among beneficiaries, including both specific bequests and the residuary estate. This also accounts for contingent beneficiaries—individuals who inherit if primary or secondary beneficiaries predecease the testator or are otherwise unable to inherit. Understanding the full distribution structure, including fallback provisions, is essential to ensuring the testator’s wishes are honored and that all potential beneficiaries are accounted for.", "type": "Open-ended", "outputConstraint": null, "example": "", "possibleValues": "Open-ended", "significance": "LOW"}, {"name": "Appointed_Executors", "description": "Indicates whether there are appointed executors for the estate. executors are responsible for managing the estate according to the will and ensuring that the wishes of the testator are fulfilled. the presence of appointed executors is crucial for the legal administration of the estate.", "type": "Boolean", "outputConstraint": "true,false", "example": "", "possibleValues": "true,false", "significance": "LOW"}, {"name": "Beneficiary_Survival", "description": "Assesses whether a beneficiary survives the testator. the survival of beneficiaries can significantly impact the distribution of the estate, especially in cases where specific provisions are made for beneficiaries who predecease the testator. understanding this aspect is essential for determining the validity of claims on the estate.", "type": "Boolean", "outputConstraint": "true,false", "example": "", "possibleValues": "true,false", "significance": "CRITICAL"}, {"name": "Child_Predeceases_Testator", "description": "Identifies whether provisions are included in the will for the scenario where a child of the testator predeceases them. This tag highlights the implications for estate distribution, such as the redistribution of the deceased child’s share to their descendants or other beneficiaries. Clarifying this situation is essential for ensuring the estate is managed in accordance with the testator’s intentions.", "type": "Boolean", "outputConstraint": "true,false", "example": "", "possibleValues": "true,false", "significance": "HIGH"}, {"name": "Conditions_for_Gifts", "description": "Identifies whether specific named gifts in the will have conditions attached to them. These conditions may dictate how and when the named beneficiaries receive their designated gifts. Conditions can include stipulations related to the beneficiary’s age, life circumstances, or specific actions they must take to qualify for the gift. Understanding these conditions is essential for ensuring the proper execution of the will.", "type": "Boolean", "outputConstraint": "true,false", "example": "", "possibleValues": "true,false", "significance": "HIGH"}, {"name": "Funeral_Arrangements_Specified", "description": "Assesses whether the will includes specific funeral arrangements as expressed by the testator. funeral arrangements can reflect the personal wishes of the testator and may include details about burial, cremation, or other memorial services. understanding these wishes is important for honoring the testator's preferences.", "type": "Boolean", "outputConstraint": "true,false", "example": "", "possibleValues": "true,false", "significance": "LOW"}, {"name": "Residuary_Estate_Division", "description": "Indicates whether there are specific provisions for how the residuary estate is to be divided among beneficiaries. the residuary estate is what remains after all debts, taxes, and specific bequests have been paid. clear instructions on the division of the residuary estate are crucial for ensuring that the testator's final wishes are honored.", "type": "Boolean", "outputConstraint": "true,false", "example": "", "possibleValues": "true,false", "significance": "LOW"}, {"name": "Legal_Provisions_Incorporated", "description": "Identifies whether the will incorporates specific legal provisions that govern the distribution of the estate. legal provisions can include statutory requirements, rights of beneficiaries, and other legal frameworks that affect how the estate is managed. understanding these provisions is essential for compliance and proper estate administration.", "type": "Boolean", "outputConstraint": "true,false", "example": "", "possibleValues": "true,false", "significance": "LOW"}, {"name": "Executor_Responsibilities_Defined", "description": "Indicates whether the responsibilities of the executor are clearly defined in the will. executors have a fiduciary duty to manage the estate responsibly and in accordance with the testator's wishes. clarity on these responsibilities is vital for the effective administration of the estate.", "type": "Boolean", "outputConstraint": "true,false", "example": "", "possibleValues": "true,false", "significance": "LOW"}, {"name": "Executor_Role", "description": "The role assigned to individuals responsible for managing the estate of the deceased, including executing the will and ensuring the distribution of assets according to the testator's wishes.", "type": "Open-ended", "outputConstraint": "executor,executrix,trustee,guardian", "example": "", "possibleValues": "Open-ended", "significance": "HIGH"}, {"name": "Beneficiary_Status", "description": "The status of individuals or entities entitled to receive benefits from the estate as outlined in the will. this includes conditions under which they may inherit or lose their rights.", "type": "Open-ended", "outputConstraint": "primary,contingent,predeceased,disinherited", "example": "", "possibleValues": "Open-ended", "significance": "LOW"}, {"name": "Key_Provisions", "description": "Important clauses or stipulations included in the will that outline the testator's wishes regarding the distribution of their estate, including any special conditions or instructions.", "type": "Open-ended", "outputConstraint": "specific bequests,trust provisions,funeral arrangements,organ donation conditions", "example": "", "possibleValues": "Open-ended", "significance": "LOW"}, {"name": "Child_Predeceasing_Testator", "description": "Conditions and implications regarding the estate if a child of the testator dies before the testator, including how their share is handled.", "type": "Open-ended", "outputConstraint": "share passes to siblings,share goes to grandchildren,share lapses,specific provisions apply", "example": "", "possibleValues": "Open-ended", "significance": "LOW"}, {"name": "Legal_Provisions", "description": "Legal clauses included in the will that outline the framework under which the estate is to be administered, including any exclusions or specific legal requirements.", "type": "Open-ended", "outputConstraint": "inheritance laws,marital provisions,disinheritance clauses,trust laws", "example": "", "possibleValues": "Open-ended", "significance": "CRITICAL"}, {"name": "Survival_Clause", "description": "A clause that specifies the conditions under which beneficiaries must survive the testator to inherit, often including a specific time frame.", "type": "Open-ended", "outputConstraint": "28 days,30 days,no specific time,must survive testator", "example": "", "possibleValues": "Open-ended", "significance": "LOW"}, {"name": "Distribution_Conditions", "description": "Conditions that must be met for beneficiaries to receive their shares of the estate, which may include age requirements, residency, or other stipulations.", "type": "Open-ended", "outputConstraint": "age of majority,residency requirements,conditions of good behavior,completion of education", "example": "", "possibleValues": "Open-ended", "significance": "HIGH"}, {"name": "Testator_Identity", "description": "Identifies the individual creating the will, including their name and address.", "type": "Open-ended", "outputConstraint": null, "example": "Input: \"I am <PERSON> of 10 Downing Street, London, SW1A 2AA, United Kingdom.\"\nOutput: \"Jane <PERSON>, 10 Downing Street, London, SW1A 2AA, United Kingdom\"", "possibleValues": "Open-ended", "significance": "HIGH"}, {"name": "Will_Date", "description": "The date when the will was signed and became effective. If day and month are unreadable because hand-written, try to extract at least the year.", "type": "Open-ended", "outputConstraint": null, "example": "Input: \"Dated: [signature date at the end of the document]\"\nOutput: \"[Exact date or just year of the will]\"", "possibleValues": "Open-ended", "significance": "HIGH"}, {"name": "Solicitor_Name", "description": "Identifies the solicitor firm who wrote the will.", "type": "Open-ended", "outputConstraint": null, "example": "", "possibleValues": "Open-ended", "significance": "HIGH"}, {"name": "NRB", "description": "In 2007 the law changed in that the Nil Rate Band could be transferred on the first death to the surviving spouse. Rules are now that when I die I get a Nil Rate Band amount of £325k before my estate pays tax, if I leave everything to my wife that would be exempt from IHT so I would not need to use my Nil Rate Band. When my wife dies her estate can claim my unused Nil Rate Band so her estate would have two lots (£325k x 2). Pre 2007 this was not the case and wills would often be written to include a gift up to the Nil Rate Band to utilise that tax free amount (giving this gift to a non-exempt person like a child). All wills that have a gift of the Nil Rate Band (Or Nil Rate Band Trust) and are dated pre 2007 should be looked at. This could be reffered to as either Nil Rate Band or Gift of Nil Rate Band or Nil Rate Band Trust or the nil per cent rate of tax in Schedule 1 to the Inheritance Tax Act 1984 [or similar wording]", "type": "Open-ended", "outputConstraint": null, "example": "Additionally, he has an NRB discretionary trust established in 2005, which necessitates rewriting to align with current regulations", "possibleValues": "Open-ended", "significance": "HIGH"}, {"name": "Has_Trusts", "description": "Determines whether the will establishes one or more trusts. Trusts may include discretionary trusts, life interest trusts, charitable trusts, or Nil Rate Band (NRB) trusts. The presence of a trust can significantly impact estate administration and tax planning, requiring further legal review.", "type": "Boolean", "outputConstraint": "true,false", "example": "Input: 'I leave my estate in trust for my children until they reach the age of 25.'\nOutput: 'Yes'", "possibleValues": "true,false", "significance": "HIGH"}, {"name": "Has_Children", "description": "Determines whether the testator has referenced children in their will. This includes naming children as beneficiaries, appointing guardians, or leaving specific provisions for them. Identifying the presence of children is important for evaluating inheritance structures and guardianship provisions.", "type": "Boolean", "outputConstraint": "true,false", "example": "Input: 'I leave my estate to my children, <PERSON> and <PERSON>.'\nOutput: 'Yes'", "possibleValues": "true,false", "significance": "HIGH"}, {"name": "Has_Property", "description": "Establishes whether the testator owns real estate, which could include houses, apartments, land, or commercial property. Property ownership is significant for estate valuation and distribution.", "type": "Boolean", "outputConstraint": "true,false", "example": "Input: 'I bequeath my house at 10 Downing Street to my son.'\nOutput: 'Yes'", "possibleValues": "true,false", "significance": "HIGH"}, {"name": "Has_Business", "description": "Determines whether the testator owns or has a share in a business. Business ownership can impact succession planning and may require special legal provisions for continuity.", "type": "Boolean", "outputConstraint": "true,false", "example": "Input: 'I leave my 50% stake in Acme Corporation to my daughter.'\nOutput: 'Yes'", "possibleValues": "true,false", "significance": "HIGH"}, {"name": "Marital_Status", "description": "Identifies whether the testator is married, widowed, or divorced at the time of writing the will. This information is relevant for understanding spousal inheritance rights and tax implications.", "type": "Open-ended", "outputConstraint": "Married,Single,Divorced,Widowed", "example": "Input: 'I leave everything to my wife, <PERSON>.'\nOutput: 'Married'", "possibleValues": "Married,Single,Divorced,Widowed", "significance": "HIGH"}, {"name": "Disabled_Person_Mention", "description": "Determines whether the will mentions a beneficiary or dependent with a disability. This can affect the structuring of inheritances, such as setting up discretionary trusts to protect their interests.", "type": "Boolean", "outputConstraint": "true,false", "example": "Input: 'I establish a trust for my son, who has special needs.'\nOutput: 'Yes'", "possibleValues": "true,false", "significance": "HIGH"}, {"name": "<PERSON>_Pets", "description": "Indicates whether the testator has referenced pets in their will, including provisions for their care or naming a guardian. This ensures that any pet-related wishes are properly considered in estate planning.", "type": "Boolean", "outputConstraint": "true,false", "example": "Input: 'I leave £5,000 for the care of my dog, <PERSON>.'\nOutput: 'Yes'", "possibleValues": "true,false", "significance": "MEDIUM"}]