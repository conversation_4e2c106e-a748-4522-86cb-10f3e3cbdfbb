Based on these tag templates, generate tags for the content of this will document.

All of the generated tags must be based on a tag template.

Generate the tags for this will document in a JSON format.

Please make sure you extract values for all the tags in the tag templates.

# Format
- Your response should use the `slug` field in the tags as the key
- Values should only be strings OR booleans, convert to string if necessary
- If the type is "OpenEnded" and the tag is found, return the value found in the document, otherwise return "Not found".
- If the type is "Boolean" only return true or false, strictly no other values are permitted.
