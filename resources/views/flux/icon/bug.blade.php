{{-- Credit: Lucide (https://lucide.dev) --}}

@props([
    'variant' => 'outline',
])

@php
if ($variant === 'solid') {
    throw new \Exception('The "solid" variant is not supported in Lucide.');
}

$classes = Flux::classes('shrink-0')
    ->add(match($variant) {
        'outline' => '[:where(&)]:size-6',
        'solid' => '[:where(&)]:size-6',
        'mini' => '[:where(&)]:size-5',
        'micro' => '[:where(&)]:size-4',
    });

$strokeWidth = match ($variant) {
    'outline' => 2,
    'mini' => 2.25,
    'micro' => 2.5,
};
@endphp

<svg
    {{ $attributes->class($classes) }}
    data-flux-icon
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    stroke-width="{{ $strokeWidth }}"
    stroke-linecap="round"
    stroke-linejoin="round"
    aria-hidden="true"
    data-slot="icon"
>
  <path d="m8 2 1.88 1.88" />
  <path d="M14.12 3.88 16 2" />
  <path d="M9 7.13v-1a3.003 3.003 0 1 1 6 0v1" />
  <path d="M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6" />
  <path d="M12 20v-9" />
  <path d="M6.53 9C4.6 8.8 3 7.1 3 5" />
  <path d="M6 13H2" />
  <path d="M3 21c0-2.1 1.7-3.9 3.8-4" />
  <path d="M20.97 5c0 2.1-1.6 3.8-3.5 4" />
  <path d="M22 13h-4" />
  <path d="M17.2 17c2.1.1 3.8 1.9 3.8 4" />
</svg>
