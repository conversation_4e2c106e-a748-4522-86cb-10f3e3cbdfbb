<?php

use App\Domains\Leads\Models\Deal;
use App\Domains\Leads\Models\LeadSource;
use App\Exports\DealsExport;
use Carbon\Carbon;
use Flux\Flux;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Url;
use Livewire\Volt\Component;
use Livewire\WithPagination;

new #[Layout('layouts.app')] class extends Component
{
    use WithPagination;

    #[Url]
    public string $callMode = 'all';

    #[Url]
    public bool $debug = false;

    #[Url]
    public array $selectedSources = [];

    #[Url]
    public ?string $search = null;

    public function openHubspotLink($dealId)
    {
        $deal = Deal::findOrFail($dealId);

        $cacheKey = 'locked-deal-' . $dealId;

        if (Cache::get($cacheKey)) {
            return Flux::modal('locked-deal')->show();
        }

        Cache::put($cacheKey, true, now()->addMinutes(5));

        // Get the HubSpot URL
        $hubspotUrl = 'https://app.hubspot.com/contacts/' . config('services.hubspot.portal_id') . "/record/0-3/{$deal->deal_object_id}";

        // Log the URL for debugging
        logger()->info('Opening HubSpot URL: ' . $hubspotUrl);

        // Open the URL directly using Livewire's JavaScript API
        $this->js("window.open('{$hubspotUrl}', '_blank')");
    }

    public function export()
    {
        $filename = 'deals-' . date('Y-m-d-H-i-s') . '.xlsx';

        return Excel::download(new DealsExport, $filename);
    }

    public function updated(string $field): void
    {
        if ($field === 'debug') {
            unset($this->deals);
        }
    }

    #[Computed(persist: true)]
    public function deals(): LengthAwarePaginator
    {
        return Deal::activeSources()
            ->when($this->search, fn (Builder $query, string $search) => $query->whereLike('deal_name', "%{$search}%"))
            ->when($this->selectedSources,
                fn (Builder $query, array $sources) => $query->whereIn('lead_source', $sources))
            // Apply consistent filtering regardless of debug mode
            ->whereIn('deal_stage', config('deal_stages.stage_ids'))
            ->whereRaw('call_count < jsonb_array_length(call_slas::jsonb)')
            ->when($this->callMode === 'first', fn (Builder $query) => $query->where('call_count', 0))
            ->when($this->callMode === 'subsequent', fn (Builder $query) => $query->where('call_count', '>', 0))
            ->whereNotNull('ranking')
            ->whereNotNull('call_slas')
            ->orderBy('ranking', 'desc')
            ->paginate(50);
    }

    #[Computed(persist: true)]
    public function sources(): Collection
    {
        return LeadSource::select('name', 'slug')->get();
    }

    #[Computed]
    public function dealCount(): int
    {
        return Cache::flexible('radar.deal-count', [10, 15], function () {
            return Deal::activeSources()
                // Apply consistent filtering regardless of debug mode
                ->whereIn('deal_stage', config('deal_stages.stage_ids'))
                ->whereRaw('call_count < jsonb_array_length(call_slas::jsonb)')
                ->whereNotNull('ranking')
                ->whereNotNull('call_slas')
                ->count();
        });
    }

    #[Computed]
    public function callCount(): int
    {
        $key = 'call-count-' . Carbon::now()->format('dmy');

        return Cache::get($key, 0);
    }

    #[Computed]
    public function breachCount(): int
    {
        return Cache::flexible('radar.breach-count', [10, 15], function () {
            return Deal::activeSources()
                // Apply consistent filtering regardless of debug mode
                ->whereIn('deal_stage', config('deal_stages.stage_ids'))
                ->whereRaw('call_count < jsonb_array_length(call_slas::jsonb)')
                ->whereNotNull('ranking')
                ->whereNotNull('call_slas')
                ->whereRaw('
                    (call_slas::jsonb->call_count)::text::timestamp < ?
                ', [Carbon::now('Europe/London')]) // Check only the current call's SLA
                ->count();
        });
    }
};
?>

<x-slot name="header">
    <div class="flex items-center">
        <h2 class="font-semibold text-xl text-gray-800 flex gap-x-2 dark:text-gray-200 leading-tight">
            <flux:icon name="handshake"/>
            {{ __('Deals') }}
        </h2>
        <flux:spacer/>
        <div class="flex gap-x-2">
            <flux:button variant="outline" icon="eye" :href="route('deals.lead_sources.index')" inset="top bottom">
                {{ __('Lead Sources') }}
            </flux:button>
        </div>
    </div>
</x-slot>
<div class="py-6">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">

        <div class="flex gap-x-6 mb-8">
            <flux:card size="sm" class="flex-1">
                <flux:heading class="flex items-center gap-2">Active Deals
                    <flux:icon name="arrow-up-right" class="ml-auto text-zinc-400" variant="micro"/>
                </flux:heading>
                <flux:text class="my-2 text-5xl text-purple-800">{{ $this->dealCount() }}</flux:text>
                <flux:subheading class="text-xs">Deals currently in flight.</flux:subheading>
            </flux:card>

            <flux:card size="sm" class="flex-1">
                <flux:heading class="flex items-center gap-2">Breaches
                    <flux:icon name="siren" class="ml-auto text-zinc-400" variant="micro"/>
                </flux:heading>
                <flux:text class="my-2 text-5xl text-rose-600">{{ $this->breachCount() }}</flux:text>
                <flux:subheading class="text-xs">deals with breached current SLA</flux:subheading>
            </flux:card>

        </div>

        <div class="flex items-center justify-end gap-x-4 mb-4">
            <div class="flex items-center gap-x-4">
                <flux:radio.group wire:model.live="callMode" variant="segmented" size="sm">
                    <flux:radio label="All" value="all"/>
                    <flux:radio label="No Calls" value="first"/>
                    <flux:radio label="1+ Call" value="subsequent"/>
                </flux:radio.group>


                <div class="flex items-center gap-x-2">
                    <flux:icon name="bug" variant="micro" :class="$debug ? 'text-green-600' : 'text-zinc-400'"/>
                    <flux:switch wire:model.live="debug"/>

                </div>
            </div>

            <div class="w-full"></div>
            <flux:button variant="subtle" icon="download" wire:click="export()" inset="top bottom">
                {{ __('Export All') }}
            </flux:button>
            <flux:input type="search" size="md" icon="magnifying-glass" wire:model.live="search"
                        placeholder="Search Deal names..."/>
            <flux:select variant="listbox" multiple wire:model.live="selectedSources" size="md" placeholder="Source"
                         searchable clearable>
                @foreach ($this->sources as $source)
                    <flux:select.option value="{{ $source->name }}">{{ $source->name }}</flux:select.option>
                @endforeach
            </flux:select>
        </div>
        <div class="bg-white p-4 dark:bg-gray-800 overflow-hidden shadow-xs sm:rounded-lg">
            <flux:table wire:poll.60s.keep-alive :paginate="$this->deals()">
                <flux:table.columns>
                    <flux:table.column>{{ __('Deal') }}</flux:table.column>
                    <flux:table.column>{{ __('Stage') }}</flux:table.column>
                    <flux:table.column>{{ __('Call Attempts') }}</flux:table.column>
                    <flux:table.column>{{ __('Next Call') }}</flux:table.column>
                    <flux:table.column/>
                </flux:table.columns>
                <flux:table.rows>
                    @foreach ($this->deals() as $deal)
                        <flux:table.row :key="$deal->id"
                                        :class="$deal->isSlaBreached() ? 'bg-rose-50 dark:bg-rose-900' : ''">
                            <flux:table.cell>
                                <div class="pl-1">
                                    <strong>{{ $deal->deal_name }}</strong><br/>
                                    {{ $deal->lead_source }}
                                    <div wire:show="debug"
                                         class="bg-amber-50 border mt-4 border-amber-100 dark:bg-amber-800 dark:border-amber-900 p-2">
                                        <div>
                                            <strong>{{ __('Score') }}:</strong>
                                            {{ $deal->ranking }}
                                        </div>
                                        <div>
                                            <strong>{{ __('Deal Created') }}:</strong>
                                            {{ $deal->deal_created_at->format('d/m/y H:i a') }}
                                        </div>
                                        <div class="mt-2">
                                            <strong>{{ __('Call SLAs') }}:</strong>
                                            <div class="mt-1 space-y-1">
                                                @foreach ($deal->call_slas as $index => $sla)
                                                    <div
                                                        class="text-sm {{ $index === $deal->call_count ? 'font-semibold text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400' }}">
                                                        Call {{ $index + 1 }}
                                                        : {{ \Carbon\Carbon::parse($sla)->format('d/m/y H:i a') }}
                                                        @if ($index === $deal->call_count)
                                                            <span class="text-xs">(current)</span>
                                                        @endif
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                {{ $deal?->dealStage?->label }}
                            </flux:table.cell>
                            <flux:table.cell>{{ $deal->call_count }}
                                / {{ count($deal->call_slas) }}</flux:table.cell>
                            <flux:table.cell>
                                {{ $deal->nextCall()->format('d/m/Y H:i A') }} <br/>
                                {{ $deal->timeToNextCall() }}
                            </flux:table.cell>
                            <flux:table.cell class="flex justify-end gap-x-2 items-center ">
                                <flux:button wire:click="openHubspotLink({{ $deal->id }})" size="sm"
                                             class="mr-1" icon="square-arrow-out-up-right">
                                    Hubspot
                                </flux:button>
                            </flux:table.cell>
                        </flux:table.row>
                    @endforeach
                </flux:table.rows>
            </flux:table>
        </div>
    </div>
    <flux:modal name="locked-deal" class="md:w-96">
        <div class="p-6 space-y-6">
            <div class="flex flex-col items-center text-center">
                <flux:icon name="lock" class="w-16 h-16 text-amber-500 mb-4"/>
                <flux:heading size="lg">Deal Currently Locked</flux:heading>
                <flux:subheading class="mt-2">
                    This deal is currently being viewed by another team member. Please try again later.
                </flux:subheading>
            </div>
            <div class="flex justify-center mt-6">
                <flux:modal.close>
                    <flux:button variant="primary">
                        Close
                    </flux:button>
                </flux:modal.close>
            </div>
        </div>
    </flux:modal>
</div>
