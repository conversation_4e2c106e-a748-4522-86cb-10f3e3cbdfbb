<?php
use App\Domains\Leads\Forms\LeadSourceForm;
use App\Domains\Leads\Models\LeadSource;
use Flux\Flux;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;
use Livewire\Volt\Component;

new #[Layout('layouts.app')] class extends Component
{
    use \Livewire\WithPagination;

    /**
     * The maximum length for string fields.
     */
    public const int MAX_STRING_LENGTH = 36;

    /**
     * The search term for the customer name.
     */
    #[Url]
    public ?string $search = null;

    /**
     * The selected column for sorting by.
     */
    #[Url]
    public string $sortBy = 'updated_at';

    /**
     * The direction for sorting.
     */
    #[Url]
    public string $sortDirection = 'desc';

    /**
     * Variable to hold a new SLA value in the LeadSourceForm input.
     */
    public ?int $newSLA = null;

    /**
     * The form for creating or editing a lead source.
     */
    public LeadSourceForm $form;

    public function mount(): void
    {
        $this->form = new LeadSourceForm($this, 'lead_source_form');
    }

    #[Computed(persist: true)]
    public function results(): LengthAwarePaginator
    {
        return LeadSource::query()
            ->when($this->search, fn (Builder $query, string $search): Builder => $query->whereLike('name', "%{$search}%"))
            ->when($this->sortBy, fn (Builder $query, string $sortBy): Builder => $query->orderBy($sortBy, $this->sortDirection))
            ->paginate(20);
    }

    public function updated(string $field): void
    {
        if ($field === 'search') {
            $this->resetPage();
        }
        if ($field === 'form.name') {
            $this->dispatch('form_name_updated');
        }
    }

    /**
     * Sort the leads by the given column.
     */
    public function sort(string $column): void
    {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    /**
     * Open the modal for creating or editing a lead source.
     */
    public function edit(LeadSource $leadSource): void
    {
        $this->form->setForm($leadSource);
        Flux::modal('lead-source-form')->show();
    }

    /**
     * Get the maximum number of calls for the lead source.
     */
    #[Computed]
    public function maxCalls(): int
    {
        return $this->form->call_slas ? count($this->form->call_slas) : 0;
    }

    #[On('form_name_updated')]
    public function sourceSlug(): string
    {
        return $this->form->sourceSlug();
    }

    public function addSLA(): void
    {
        if ($this->newSLA !== null) {
            $this->form->call_slas[] = $this->newSLA;
            $this->newSLA = null;
        }
    }

    public function removeSLA(int $sla): void
    {
        $this->form->call_slas = array_filter($this->form->call_slas, fn ($item): bool => $item !== $sla);
    }

    public function clearForm(): void
    {
        $this->form = new LeadSourceForm($this, 'form');
        $this->newSLA = null;
        $this->resetErrorBag();
    }

    public function save(): void
    {
        $this->form->save();
        Flux::modal('lead-source-form')->close();
        Flux::toast(variant: 'success', text: 'Your changes have been saved.');
        unset($this->results);
    }
};
?>

<x-slot name="header" wire:model="search">
    <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 flex gap-x-2 dark:text-gray-200 leading-tight">
            <flux:icon name="handshake" />
            {{ __('Deals - Lead Sources') }}
        </h2>
        <flux:modal.trigger name="lead-source-form">
            <flux:button variant="primary" icon="plus" inset="top bottom" class="cursor-pointer">
                {{ __('Add Lead Source') }}
            </flux:button>
        </flux:modal.trigger>
    </div>
</x-slot>
<div class="flex flex-col gap-5 max-w-7xl mx-auto p-6">
    {{-- LeadSource search  --}}
    <flux:input wire:model.live.debounce.500ms="search" placeholder="Search lead source name here..."
        icon="magnifying-glass" class="self-end max-w-1/3" />
    {{-- LeadSource list --}}
    <x-flux::card>
        <flux:table :paginate="$this->results()">
            <flux:table.columns>
                <flux:table.column>
                    {{ __('Name') }}
                </flux:table.column>
                <flux:table.column>
                    {{ __('Slug') }}
                </flux:table.column>
                <flux:table.column>
                    {{ __('Max Calls') }}
                </flux:table.column>
                <flux:table.column>
                    {{ __('Ranking') }}
                </flux:table.column>
                <flux:table.column>
                    {{ __('Last Updated') }}
                </flux:table.column>
                <flux:table.column />
            </flux:table.columns>
            <flux:table.rows>
                @foreach ($this->results() as $leadSource)
                    <flux:table.row :key="$leadSource->id">
                        <flux:table.cell>
                            @if (strlen($leadSource->name) > self::MAX_STRING_LENGTH)
                                <flux:tooltip content="{{ $leadSource->name }}">
                                    <span>
                                        {{ Str::limit($leadSource->name, self::MAX_STRING_LENGTH) }}
                                    </span>
                                </flux:tooltip>
                            @else
                                {{ $leadSource->name }}
                            @endif
                        </flux:table.cell>
                        <flux:table.cell>
                            @if (strlen($leadSource->slug) > self::MAX_STRING_LENGTH)
                                <flux:tooltip content="{{ $leadSource->slug }}">
                                    <span>
                                        {{ Str::limit($leadSource->slug, self::MAX_STRING_LENGTH) }}
                                    </span>
                                </flux:tooltip>
                            @else
                                {{ $leadSource->slug }}
                            @endif
                        </flux:table.cell>
                        <flux:table.cell>
                            {{ $leadSource->max_calls }}
                        </flux:table.cell>
                        <flux:table.cell>
                            {{ $leadSource->ranking }}
                        </flux:table.cell>
                        <flux:table.cell>
                            {{ $leadSource->updated_at }}
                        </flux:table.cell>
                        <flux:table.cell class="flex items-center">
                            <flux:button variant="ghost" size="sm" icon="pencil" inset="top bottom"
                                wire:click="edit({{ $leadSource->id }})" class="cursor-pointer">
                                {{ __('Edit') }}
                            </flux:button>
                        </flux:table.cell>
                    </flux:table.row>
                @endforeach
            </flux:table.rows>
        </flux:table>
    </x-flux::card>
    <flux:modal name="lead-source-form" wire:close="clearForm" class="w-128">
        <form wire:submit="save" class="flex flex-col gap-3 py-8 px-4">
            <flux:input type="text" wire:model.change="form.name" placeholder="Name"
                label="{{ __('Name') }}:" />
            <flux:label>{{ __('Slug') }}:</flux:label>
            <flux:description>{{ $this->sourceSlug() }}</flux:description>
            <flux:textarea wire:model="form.description" placeholder="Description" label="{{ __('Description') }}:" />
            <flux:label for="call_slas">
                {{ __('Call SLAs') }}:
            </flux:label>
            <flux:field>
                <div class="flex gap-x-2">
                    <flux:input type="number" placeholder="Enter a new call SLA (in minutes)" wire:model="newSLA"
                        :invalid="$errors->get('form.call_slas')" />
                    <flux:button wire:click="addSLA" type="button">{{ __('Add') }}</flux:button>
                </div>
                <flux:error name="form.call_slas" />
                <flux:error name="form.call_slas.*" />
            </flux:field>
            <div>
                @foreach ($this->form->call_slas as $sla)
                    <flux:tooltip content="{{ Carbon\CarbonInterval::minutes($sla)->cascade()->forHumans() }}">
                        <flux:badge class="my-1" color="purple">
                            {{ Carbon\CarbonInterval::minutes($sla) }}
                            <flux:badge.close wire:click="removeSLA({{ $sla }})" class="cursor-pointer" />
                        </flux:badge>
                    </flux:tooltip>
                @endforeach
            </div>
            <flux:label>{{ __('Max Calls') }}:</flux:label>
            <flux:description>{{ $this->maxCalls() }}</flux:description>
            <flux:input type="number" wire:model="form.ranking" placeholder="Ranking" label="{{ __('Ranking') }}:" />
            <flux:button variant="primary" type="submit" class="mt-4 self-end">
                {{ __('Save Lead Source') }}
            </flux:button>
        </form>
    </flux:modal>
</div>
