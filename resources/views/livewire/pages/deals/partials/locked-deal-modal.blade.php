<?php

use Flux\Flux;
use Livewire\Volt\Component;

new class extends Component
{
    public function close()
    {
        Flux::modal('locked-deal')->close();
    }
}; ?>

<flux:modal name="locked-deal" class="md:w-96">
    <div class="p-6 space-y-6">
        <div class="flex flex-col items-center text-center">
            <flux:icon name="lock" class="w-16 h-16 text-amber-500 mb-4" />
            <flux:heading size="lg">Deal Currently Locked</flux:heading>
            <flux:subheading class="mt-2">
                This deal is currently being viewed by another team member. Please try again later.
            </flux:subheading>
        </div>

        <div class="flex justify-center mt-6">
            <flux:button wire:click="close" variant="primary">
                Close
            </flux:button>
        </div>
    </div>
</flux:modal>
