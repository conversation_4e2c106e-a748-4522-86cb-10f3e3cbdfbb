<?php

use App\Domains\Radar\Models\DocumentTag;
use App\Domains\Radar\Models\RadarResult;
use App\Domains\Radar\Services\Export\Radar\RadarExport;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Url;
use Livewire\Volt\Component;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

new #[Layout('layouts.app')] class extends Component
{
    use \Livewire\WithPagination;

    #[Url]
    public ?int $selectedTag = null;

    #[Url]
    public string $sortBy = 'id';

    #[Url]
    public ?string $search = null;

    #[Url]
    public string $sortDirection = 'desc';

    public function sort($column): void
    {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function download(): BinaryFileResponse
    {
        $filename = 'radar-' . date('Y-m-d-H-i-s') . '.xlsx';

        return Excel::download(new RadarExport($this->selectedTag), $filename);
    }

    #[Computed]
    public function results(): LengthAwarePaginator
    {
        return RadarResult::query()
            ->when($this->search, function (Builder $query, string $search) {
                return $query->whereHas('document', fn (Builder $query) => $query->where('reference', 'like', "%{$search}%"));
            })
            ->when($this->selectedTag, function (Builder $query, int $selectedTag) {
                return $query->whereHas('documentTags',
                    fn ($query) => $query->where('id', $selectedTag));
            })
            ->where('status', '!=', 'failed')
            ->when($this->sortBy, function (Builder $query, string $sortBy) {
                return $query->orderBy($this->sortBy, $this->sortDirection);
            })
            ->paginate(20);
    }

    #[Computed]
    public function tags()
    {
        return DocumentTag::query()
            ->get();
    }

    #[Computed]
    public function scoreStyle(?int $score)
    {
        if ($score) {
            if ($score >= 70) {
                return 'bg-green-100 text-green-900';
            }

            if ($score >= 50) {
                return 'bg-yellow-100 text-yellow-900';
            }

            return 'bg-red-100 text-red-900';
        }

        return 'bg-gray-100 text-gray-900';
    }
}
?>
<div>

    <x-slot name="header">
        <div class="flex items-center">
            <h2 class="font-semibold text-xl text-gray-800 flex gap-x-2 dark:text-gray-200 leading-tight">
                <flux:icon name="radar"/>
                Radar
            </h2>
            <flux:spacer/>
            <div class="flex items-center gap-x-2">
                <livewire:pages.radar.partials.import_modal/>
                <livewire:pages.radar.partials.upload_document_modal/>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">

            <div class="flex items-center justify-end gap-x-4 mb-4">

                <div class="w-full"></div>

                <flux:input type="search"
                            size="md"
                            icon="magnifying-glass"
                            wire:model.live="search"
                            placeholder="Search Radar Results..."/>

                <flux:select
                    variant="listbox"
                    wire:model.live="selectedTag"
                    size="md"
                    placeholder="Tags"
                    searchable
                    clearable>
                    @foreach ($this->tags as $tag)
                        <flux:select.option value="{{ $tag->id }}">{{ $tag->name }}</flux:select.option>
                    @endforeach
                </flux:select>
            </div>


            <div class="bg-white p-4 dark:bg-gray-800 overflow-hidden shadow-xs sm:rounded-lg">


                <div class="flex items-center mb-4 gap-x-2">
                    <div class="flex items-center gap-x-4">
                        <flux:heading size="lg">Radar Results</flux:heading>
                    </div>
                    <flux:spacer/>
                    <flux:button variant="subtle" wire:click="download" icon="download">Export</flux:button>
                </div>


                <flux:table :paginate="$this->results">
                    <flux:table.columns>
                        <flux:table.column>Document</flux:table.column>
                        <flux:table.column>Status</flux:table.column>
                        <flux:table.column sortable
                                           :sorted="$sortBy === 'created_at'"
                                           :direction="$sortDirection"
                                           wire:click="sort('created_at')">
                            Date Uploaded
                        </flux:table.column>
                        <flux:table.column sortable
                                           :sorted="$sortBy === 'score'"
                                           :direction="$sortDirection"
                                           wire:click="sort('score')"
                        >Score
                        </flux:table.column>
                        <flux:table.column></flux:table.column>
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->results as $result)
                            <flux:table.row :key="$result->id">
                                <flux:table.cell>
                                    {{ $result->document->reference }}
                                </flux:table.cell>

                                <flux:table.cell>

                                    @if (!$result->ocr_complete)
                                        <flux:badge icon="circle-ellipsis" color="gray" inset="top bottom">OCR
                                        </flux:badge>
                                    @else
                                        <flux:badge icon="circle-check" color="green" inset="top bottom">OCR
                                        </flux:badge>
                                    @endif

                                    @if (!$result->tag_extraction_complete)
                                        <flux:badge icon="circle-ellipsis" color="gray" inset="top bottom">Tags
                                        </flux:badge>
                                    @else
                                        <flux:badge icon="circle-check" color="green" inset="top bottom">Tags
                                        </flux:badge>
                                    @endif

                                    @if (!$result->insight_generation_complete)
                                        <flux:badge icon="circle-ellipsis" color="gray" inset="top bottom">Insights
                                        </flux:badge>
                                    @else
                                        <flux:badge icon="circle-check" color="green" inset="top bottom">Insights
                                        </flux:badge>
                                    @endif

                                    @if (!$result->summary_generation_complete)
                                        <flux:badge icon="circle-ellipsis" color="gray" inset="top bottom">Summary
                                        </flux:badge>
                                    @else
                                        <flux:badge icon="circle-check" color="green" inset="top bottom">Summary
                                        </flux:badge>
                                    @endif
                                    @if (!$result->scores_calculated)
                                        <flux:badge icon="circle-ellipsis" color="gray" inset="top bottom">Scores
                                        </flux:badge>
                                    @else
                                        <flux:badge icon="circle-check" color="green" inset="top bottom">Scores
                                        </flux:badge>
                                    @endif
                                </flux:table.cell>

                                <flux:table.cell>
                                    {{ $result->created_at->format('d/m/Y') }}
                                </flux:table.cell>

                                <flux:table.cell>
                                    <span
                                        class="py-1 px-2 rounded-sm font-bold {{ $this->scoreStyle($result->score) }}">
                                        {{ $result->score ?: '-' }}
                                    </span>
                                </flux:table.cell>

                                <flux:table.cell class="flex items-center">
                                    <flux:spacer/>
                                    <flux:button variant="ghost" wire:navigate size="sm" icon="eye"
                                                 :href="route('radar.show', $result->document->reference)"
                                                 inset="top bottom">View
                                    </flux:button>
                                </flux:table.cell>
                            </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            </div>
        </div>
    </div>
</div>
