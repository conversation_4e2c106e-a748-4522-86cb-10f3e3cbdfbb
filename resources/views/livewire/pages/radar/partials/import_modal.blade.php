<?php

use App\Domains\Radar\Jobs\Radar\ImportWill;
use App\Domains\Radar\Models\Document;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;

new #[Layout('layouts.app')] class extends Component
{
    public ?array $files = [];
    public ?string $selectedBank;
    public ?array $counts = [
        'total' => 0,
        'exists' => 0,
        'new' => 0,
    ];
    public ?bool $overwrite = false;

    public ?bool $loading = false;

    public function banks()
    {
        return Storage::disk('willbank')->directories();
    }

    #[Computed]
    public function numberToImport()
    {
        if ($this->overwrite) {
            return $this->counts['total'];
        }

        return $this->counts['new'];
    }

    public function getFiles()
    {
        $this->loading = true;

        $files = Storage::disk('willbank')->files($this->selectedBank);

        $list = collect();
        foreach ($files as $file) {
            $filename = explode('/', $file)[1];
            $ref = explode('.', $filename)[0];

            $list->push([
                'ref' => $ref,
                'exists' => in_array($ref, $this->existingFiles()),
            ]);
        }

        $this->counts = [
            'total' => $list->count(),
            'exists' => $list->where('exists', true)->count(),
            'new' => $list->where('exists', false)->count(),
        ];

        $this->files = $list->toArray();

        $this->loading = false;

    }

    public function existingFiles()
    {
        return once(function () {
            return Document::query()->select('reference')->get()->pluck('reference')->toArray();
        });
    }

    public function runImport()
    {
        $files = Storage::disk('willbank')->files($this->selectedBank);

        foreach ($files as $file) {
            ImportWill::dispatch($file, $this->selectedBank, $this->overwrite);
        }

        $this->reset('files', 'selectedBank', 'counts', 'overwrite', 'loading');
        Flux::toast('Will bank import started, this may take a few moments.');
        Flux::modal('import-willbank')->close();
    }
}

?>

<div>

    <flux:modal.trigger name="import-willbank">
        <flux:button variant="outline">Import from Will Bank</flux:button>
    </flux:modal.trigger>

    <flux:modal name="import-willbank" class="md:w-96">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Import Will Bank</flux:heading>
                <flux:subheading>Batch import an existing will bank from S3.</flux:subheading>
            </div>

            <flux:select variant="listbox" wire:model="selectedBank" label="Will Bank" wire:change="getFiles"
                         placeholder="Select a Will Bank">
                @foreach ($this->banks() as $key => $value)
                    <flux:select.option>{{ $value }}</flux:select.option>
                @endforeach
            </flux:select>


            @if ($this->files)

                <flux:table>
                    <flux:table.rows>
                        <flux:table.row>
                            <flux:table.cell>Total Wills in Bank</flux:table.cell>
                            <flux:table.cell>{{ number_format($this->counts['total']) }}</flux:table.cell>
                        </flux:table.row>

                        <flux:table.row>
                            <flux:table.cell>Wills Already in OLLIE</flux:table.cell>
                            <flux:table.cell>{{ number_format($this->counts['exists']) }}</flux:table.cell>
                        </flux:table.row>

                        <flux:table.row>
                            <flux:table.cell>Wills to Import</flux:table.cell>
                            <flux:table.cell>{{ number_format($this->numberToImport) }}</flux:table.cell>
                        </flux:table.row>

                    </flux:table.rows>
                </flux:table>

                <flux:switch label="Overwrite Existing?" wire:model.live="overwrite"/>

            @endif

            <div class="flex">
                <flux:spacer/>
                <flux:button :disabled="$this->numberToImport() < 1" :wire:loading="$this->loading" variant="primary"
                             wire:click="runImport">
                    @if ($this->numberToImport)
                        Import {{ number_format($this->numberToImport) }} wills
                    @else
                        No wills to import
                    @endif
                </flux:button>
            </div>
        </div>
    </flux:modal>

</div>
