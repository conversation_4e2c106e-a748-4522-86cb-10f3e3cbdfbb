<?php

use App\Domains\Radar\Enums\DocumentType;
use App\Domains\Radar\Facades\Batch;
use App\Domains\Radar\Models\Document;
use Flux\Flux;
use Illuminate\Support\Str;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Livewire\Volt\Component;
use Livewire\WithFileUploads;

new class extends Component
{
    use WithFileUploads;

    public ?TemporaryUploadedFile $document = null;

    public array $tags = [];

    public function clearErrors(): void
    {
        $this->resetErrorBag('documents.*');
    }

    public function mount()
    {
        $this->tags[] = Batch::newBatch();
    }

    public function uploadDocument()
    {
        $filename = $this->document->getClientOriginalName();
        $reference = explode('.', $filename)[0];
        $path = $this->document->store('documents', options: ['disk' => 'ollie']);

        $document = Document::create([
            'path' => $path,
            'type' => DocumentType::WILL,
            'disk' => 'ollie',
            'filename' => $this->document->getClientOriginalName(),
            'extension' => $this->document->getClientOriginalExtension(),
            'reference' => $reference,
        ]);

        $document->radarResult()->create([
            'user_id' => auth()->id(),
            'batch_id' => Str::random(8),
        ]);

        $document->radarResult->attachTags($this->tags);

        Flux::modal('uploadModal')->close();
        Flux::toast(variant: 'success', text: 'Document(s) uploaded successfully');

        return redirect()->route('radar.index');
    }
};

?>

<div>
    <flux:modal.trigger name="uploadModal">
        <flux:button variant="primary" icon="upload">Upload</flux:button>
    </flux:modal.trigger>

    <flux:modal name="uploadModal" class="md:w-96 space-y-6">
        <div class="mb-4">
            <flux:heading size="lg">Upload Will</flux:heading>
            <flux:subheading>Upload a single will to be processed.</flux:subheading>
        </div>
        <div class="flex flex-col gap-4">

            <flux:input type="file" wire:model="document" label="Document" />

            @error('document')
                <span class="text-red-400 text-sm whitespace-pre-wrap">{{ $message }}</span>
            @enderror

            <div x-data="{ tags: $wire.entangle('tags').live, newTag: null }">
                <flux:input type="text" placeholder="Add a tag and hit return..." label="Tags" class="w-full"
                    x-model="newTag" @keyup.enter="tags.push(newTag); newTag=''" />

                <ul class="mt-2 flex flex-wrap gap-2">
                    <template x-for="(value, index) in tags" :key="index">
                        <flux:badge icon-trailing="x" class="cursor-pointer" @click="tags.splice(index, 1)">
                            <span x-text="value"></span>
                        </flux:badge>
                    </template>
                </ul>
            </div>

            <flux:button type="submit" variant="primary" class="disabled:bg-gray-300" wire:click="uploadDocument">
                Upload
            </flux:button>
        </div>
    </flux:modal>

</div>
