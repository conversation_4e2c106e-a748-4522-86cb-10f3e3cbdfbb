<?php

use App\Domains\Radar\Enums\RadarStatus;
use App\Domains\Radar\Jobs\Radar\CalculateScore;
use App\Domains\Radar\Jobs\Radar\ExtractTags;
use App\Domains\Radar\Jobs\Radar\ExtractText;
use App\Domains\Radar\Jobs\Radar\GenerateInsights;
use App\Domains\Radar\Jobs\Radar\GenerateSummary;
use App\Domains\Radar\Jobs\Radar\OllieThinks;
use App\Domains\Radar\Models\Document;
use App\Domains\Radar\Models\RadarResult;
use Flux\Flux;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;

new #[Layout('layouts.app')] class extends Component
{
    public string $id;
    public RadarResult $result;
    public string $tab = 'tags';

    public function mount($id): void
    {
        $this->result = Document::where('reference', $id)->firstOrFail()->radarResult;
    }

    public function triggerReprocess(): void
    {
        ExtractText::withChain(chain: [
            new ExtractTags($this->result),
            new GenerateInsights($this->result),
            new GenerateSummary($this->result),
            new CalculateScore($this->result),
            new OllieThinks($this->result),
        ])->dispatch($this->result);

        $this->result->update([
            'ocr_complete' => false,
            'tag_extraction_complete' => false,
            'insight_generation_complete' => false,
            'summary_generation_complete' => false,
            'scores_calculated' => false,
            'status' => RadarStatus::PENDING->value,
        ]);

        Flux::toast(variant: 'warning', heading: 'Triggered Processing',
            text: 'This may take a few minutes, grab a coffee then refresh!');
    }

    public function scoreStyle(): string
    {
        if ($this->result->score >= 70) {
            return 'bg-green-100 text-green-900';
        }

        if ($this->result->score >= 50) {
            return 'bg-yellow-100 text-yellow-900';
        }

        return 'bg-red-100 text-red-900';
    }
}
?>
<div>

    <x-slot name="header">
        <div class="flex items-center">


            <div>
                <h2 class="mb-4 font-semibold text-xl text-gray-800 flex gap-x-2 dark:text-gray-200 leading-tight">
                    <flux:icon name="radar"/>
                    {{ $result->document->reference }}
                </h2>

                @if ($result->documentTags)
                    @foreach ($result->documentTags as $tag)
                        <flux:badge>{{ $tag->name }}</flux:badge>
                    @endforeach
                @endif
            </div>
            <flux:spacer/>

        </div>
    </x-slot>

    @if ($this->result->status === RadarStatus::PENDING->value)
        <div class="flex flex-col items-center justify-center h-svh">
            <flux:icon name="sparkles" class="animate-pulse text-gray-400 dark:text-gray-600 size-12"/>
            <div class="text-center mt-4 text-gray-400 dark:text-gray-600">
                Processing...
            </div>
        </div>
    @else

        <div class="mx-auto max-w-7xl mt-4">
            <div class="flex gap-x-4 mb-4">

                <div class="w-3/4">

                    @if ($result->ollie_thinks)
                        <div
                            class="bg-indigo-100 border border-indigo-200 dark:bg-indigo-900 dark:border-indigo-950 rounded-lg p-4 mb-4">
                            <div class="flex gap-x-4">
                                <flux:icon name="sparkles" class="text-indigo-500 dark:text-indigo-300 mt-2"/>
                                <div>
                                    <x-markdown class="prose-sm">
                            <span
                                class="font-bold text-indigo-600 dark:text-indigo-300">Ollie thinks:</span> {{ $result->ollie_thinks }}
                                    </x-markdown>
                                </div>

                            </div>
                        </div>
                    @endif

                    <flux:card>

                        <div class="flex items-center">
                            <flux:heading size="lg">Summary</flux:heading>

                        </div>
                        <flux:separator class="my-4"/>

                        @empty($result->summary)
                            -
                        @else
                            <x-markdown class="prose-sm">
                                {{ !! $result->summary ? $result->summary : 'No summary available' }}
                            </x-markdown>
                        @endempty

                    </flux:card>

                </div>

                <div class="grow">
                    <flux:card class="mb-4">
                        <flux:subheading class="mb-4">Suitability Score</flux:subheading>
                        <flux:separator class="my-4"/>

                        <div class="text-5xl font-bold text-center p-6 rounded-xl {{ $this->scoreStyle() }}">
                            {{ $this->result->score }}
                        </div>

                    </flux:card>

                    <flux:card>
                        <flux:subheading class="mb-4">Key Information</flux:subheading>
                        <flux:separator class="my-4"/>

                        <div class="flex flex-col gap-y-1 mb-2 text-sm">
                            <span class="font-bold">Customer Age</span>
                            <span>{{ $this->result->findTag('customer_age') }}</span>
                        </div>

                        <div class="flex flex-col gap-y-1 mb-2 text-sm">
                            <span class="font-bold">Will Date</span>
                            <span>{{ $this->result->findTag('will_date') }}</span>
                        </div>

                        <div class="flex flex-col gap-y-1 mb-2 text-sm">
                            <span class="font-bold">Solicitor Firm</span>
                            <span>{{ $this->result->findTag('solicitor_name') }}</span>
                        </div>

                        <div class="flex flex-col gap-y-1 mb-2 text-sm">
                            <span class="font-bold">Trust</span>
                            <span>
                            <x-BooleanIcon :value="$this->result->findTag('trust')"/>
                        </span>
                        </div>

                        <div class="flex flex-col gap-y-1 mb-2 text-sm">
                            <span class="font-bold">Has Children</span>
                            <span>
                            <x-BooleanIcon :value="$this->result->findTag('has_children')"/>
                        </span>
                        </div>

                        <div class="flex flex-col gap-y-1 mb-2 text-sm">
                            <span class="font-bold">Has Pets</span>
                            <span>
                            <x-BooleanIcon :value="$this->result->findTag('has_pets')"/>
                        </span>
                        </div>

                        <div class="flex flex-col gap-y-1 mb-2 text-sm">
                            <span class="font-bold">Has Property</span>
                            <span>
                            <x-BooleanIcon :value="$this->result->findTag('has_property')"/>
                        </span>
                        </div>

                        <div class="flex flex-col gap-y-1 mb-2 text-sm">
                            <span class="font-bold">Has Business</span>
                            <span>
                            <x-BooleanIcon :value="$this->result->findTag('has_business')"/>
                        </span>
                        </div>

                    </flux:card>

                    <div class="text-center mt-3">
                        <flux:button variant="ghost" size="sm" icon="refresh-ccw" wire:click="triggerReprocess">
                            Reprocess This Will
                        </flux:button>
                    </div>

                </div>

            </div>

            <flux:card class="mb-4">
                <flux:tab.group>
                    <flux:tabs wire:model="tab">
                        <flux:tab name="tags">Tags</flux:tab>
                        <flux:tab name="insights">Insights</flux:tab>
                        <flux:tab name="scores">Scores</flux:tab>
                        <flux:tab name="ocr">OCR</flux:tab>
                    </flux:tabs>

                    <flux:tab.panel name="tags">

                        @empty($result->tags->count())
                            -
                        @else

                            <flux:table>
                                <flux:table.columns>
                                    <flux:table.column>Tag</flux:table.column>
                                    <flux:table.column>Value</flux:table.column>
                                </flux:table.columns>

                                <flux:table.rows>
                                    @foreach ($result->tags as $tag)
                                        <flux:table.row>
                                            <flux:table.cell
                                                class="font-bold">{{ Str::title(str_replace('_', ' ', $tag->definition->name)) }}</flux:table.cell>
                                            <flux:table.cell class="text-wrap">
                                                @if ($tag->value === '1' || $tag->value === '0')
                                                    <x-BooleanIcon :value="$tag->value"/>
                                                @else
                                                    {{ $tag->value }}
                                                @endif
                                            </flux:table.cell>
                                        </flux:table.row>
                                    @endforeach
                                </flux:table.rows>
                            </flux:table>
                        @endempty
                    </flux:tab.panel>


                    <flux:tab.panel name="insights">
                        @empty($result->insights->count())
                            -
                        @else

                            <flux:table>
                                <flux:table.columns>
                                    <flux:table.column>Insight</flux:table.column>
                                    <flux:table.column>Observation</flux:table.column>
                                </flux:table.columns>

                                <flux:table.rows>
                                    @foreach ($result->insights as $tag)
                                        <flux:table.row>
                                            <flux:table.cell
                                                class="font-bold">{{ Str::title(str_replace('_', ' ', $tag->definition->name)) }}</flux:table.cell>
                                            <flux:table.cell class="text-wrap">{{ $tag->value }}</flux:table.cell>
                                        </flux:table.row>
                                    @endforeach
                                </flux:table.rows>
                            </flux:table>

                        @endempty
                    </flux:tab.panel>

                    <flux:tab.panel name="scores">
                        @empty($result->scores)
                            -
                        @else

                            <flux:table>
                                <flux:table.columns>
                                    <flux:table.column>Rule</flux:table.column>
                                    <flux:table.column>Score rationale</flux:table.column>
                                </flux:table.columns>

                                <flux:table.rows>
                                    @foreach ($result->scores as $rule => $score)
                                        <flux:table.row>
                                            <flux:table.cell class="font-bold">
                                                {{ $rule }}
                                            </flux:table.cell>
                                            <flux:table.cell class="text-wrap">
                                                {{ $score['rationale'] }}
                                            </flux:table.cell>
                                        </flux:table.row>
                                    @endforeach
                                </flux:table.rows>
                            </flux:table>
                        @endempty
                    </flux:tab.panel>

                    <flux:tab.panel name="ocr">

                        @empty($result->ocr_text)
                            -
                        @else
                            <div class="prose-sm font-serif bg-zinc-100 dark:bg-zinc-900 text-sm! p-2 rounded-xl">
                                {!! $result->ocr_text !!}
                            </div>
                        @endempty

                    </flux:tab.panel>
                </flux:tab.group>
            </flux:card>

        </div>

    @endif

</div>
