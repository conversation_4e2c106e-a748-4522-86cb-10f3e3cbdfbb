<?php

use App\Domains\SuitabilityReports\Enums\CustomerInterestResponses;
use Livewire\Volt\Component;
use Livewire\Attributes\Modelable;

new class extends Component
{
    #[Modelable]
    public $value;

    public $options = [];
    public $label;
    public $placeholder = 'foo bar';
    public $class = '';

    public function mount()
    {
        $this->label = __('Customer Response');
        $this->options = CustomerInterestResponses::cases();
    }
}; ?>

<div class="{{ $class }}">
    <flux:field class="flex justify-between gap-x-8">
        <flux:label @class([
            'max-w-1/2',
            'text-[#FF7300]!' => !$value,
            'text-[#038748]!' => $value,
        ])>
            {{ $label }}
        </flux:label>
        <flux:select wire:model="value"
                     placeholder="{{ $placeholder }}"
                     class="!w-1/3">
            @foreach ($options as $option)
                @if (is_object($option) && isset($option->name, $option->value))
                    <flux:select.option value="{{ $option->name }}">
                        {{ $option->value }}
                    </flux:select.option>
                @endif
            @endforeach
        </flux:select>
    </flux:field>
</div>
