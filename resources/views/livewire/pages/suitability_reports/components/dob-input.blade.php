<?php

use Illuminate\Support\Carbon;

use function Livewire\Volt\computed;
use function Livewire\Volt\state;

state('value')->modelable();

state(['label' => 'What is your date of birth?', 'placeholder' => 'DD/MM/YYYY']);

$age = computed(function () {
    if (empty($this->customerDob)) {
        return '';
    }
    if (! $this->customerDob instanceof Carbon) {
        return Carbon::parse($this->customerDob)->age;
    }

    return $this->customerDob->age;
});

?>

<flux:field class="flex justify-between">
    <flux:label class="text-white!">
        {{ $label }}
    </flux:label>
    <div class="flex gap-x-4">
        <flux:date-picker selectable-header  wire:model.live.debounce="value" placeholder="{{ $placeholder }}" >
            <x-slot name="trigger">
                <flux:date-picker.input />
            </x-slot>
        </flux:date-picker>
        <flux:input :value="$this->age" class="max-w-12" disabled/>
    </div>
</flux:field>

