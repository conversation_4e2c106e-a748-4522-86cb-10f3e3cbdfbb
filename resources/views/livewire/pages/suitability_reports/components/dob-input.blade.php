<?php

use Illuminate\Support\Carbon;
use Livewire\Attributes\Modelable;
use Livewire\Volt\Component;

new class extends Component
{
    #[Modelable]
    public ?Carbon $value;

    public string $label = '';
    public string $placeholder = '';
}
?>

<flux:field class="flex justify-between">
    <flux:label class="text-white!">
        {{ $label }}
    </flux:label>
    <div class="flex gap-x-4">
        <flux:date-picker selectable-header wire:model.live.debounce="value" placeholder="{{ $placeholder }}">
            <x-slot name="trigger">
                <flux:date-picker.input/>
            </x-slot>
        </flux:date-picker>
        <flux:input :value="$this?->value?->age" class="max-w-12" disabled/>
    </div>
</flux:field>

