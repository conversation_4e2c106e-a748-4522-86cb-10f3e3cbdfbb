<?php

use Illuminate\Support\Carbon;
use Livewire\Volt\Component;
use Livewire\Attributes\Modelable;

new class extends Component
{
    #[Modelable]
    public $value;

    public $label = 'What is your date of birth?';
    public $placeholder = 'DD/MM/YYYY';

    public function getAgeProperty()
    {
        if (empty($this->customerDob)) {
            return '';
        }
        if (! $this->customerDob instanceof Carbon) {
            return Carbon::parse($this->customerDob)->age;
        }

        return $this->customerDob->age;
    }
}; ?>

<flux:field class="flex justify-between">
    <flux:label class="text-white!">
        {{ $label }}
    </flux:label>
    <div class="flex gap-x-4">
        <flux:date-picker selectable-header  wire:model.live.debounce="value" placeholder="{{ $placeholder }}" >
            <x-slot name="trigger">
                <flux:date-picker.input />
            </x-slot>
        </flux:date-picker>
        <flux:input :value="$this->age" class="max-w-12" disabled/>
    </div>
</flux:field>

