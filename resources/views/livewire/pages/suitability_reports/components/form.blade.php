<?php

use App\Domains\SuitabilityReports\Forms\SuitabilityReportForm;
use App\Domains\SuitabilityReports\Models\SuitabilityReportCustomer;
use Livewire\Attributes\Locked;
use Livewire\Attributes\Modelable;
use Livewire\Volt\Component;

new class extends Component
{
    #[Modelable]
    public SuitabilityReportForm $form;

    #[Locked]
    public SuitabilityReportCustomer $customer;

    public string $class = '';

    public function updated($property): void
    {
        // save the form to the database
        $this->form->update($this->customer->report);
    }
}
?>

<form class="{{ $class }}">
    <!-- Estate planning  -->
    <livewire:pages.suitability_reports.partials.estate-planning-questions wire:model.change="form"/>
    <!-- Family  -->
    <livewire:pages.suitability_reports.partials.family-questions wire:model.change="form"/>
    <!-- Estate -->
    <livewire:pages.suitability_reports.partials.estate-questions wire:model.change="form"/>
    <!-- Marketing Preferences -->
    <livewire:pages.suitability_reports.partials.marketing-preferences wire:model.change="form"/>
</form>
