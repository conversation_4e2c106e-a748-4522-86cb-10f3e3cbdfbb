<?php

use Livewire\Volt\Component;
use Livewire\Attributes\Modelable;

new class extends Component
{
    #[Modelable]
    public $value;

    public $label = '';
    public $suggestion = '';

    public function getDisplaySuggestionProperty()
    {
        return $this->value === '0';
    }
}; ?>

<div>
    <flux:field class="flex justify-between">
        <flux:label>
            {{ $label }}
        </flux:label>
        <flux:radio.group wire:model="value">
            <div class="flex gap-x-4 justify-center items-center">
                <flux:radio label="Yes" value="1"/>
                <flux:radio label="No" value="0"/>
            </div>
        </flux:radio.group>

    </flux:field>
    @if ($this->displaySuggestion)
        <div class="flex justify-end">
            <flux:text class="text-[#FF7300]">{{ $suggestion }}</flux:text>
        </div>
    @endif
</div>
