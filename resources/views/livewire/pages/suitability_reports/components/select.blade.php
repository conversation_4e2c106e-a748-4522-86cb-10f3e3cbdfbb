<?php

use Livewire\Attributes\Modelable;
use Livewire\Attributes\Validate;
use Livewire\Volt\Component;

new class extends Component {
    #[Modelable]
    public ?string $value;

    public string $label = '';
    public string $placeholder = '';
    public array $options = [];

    protected function rules()
    {
        return [
            'options' => [
                'required',
                'array',
                'min:1',
                function ($attribute, $value, $fail) {
                    foreach ($value as $index => $option) {
                        // Check if it's an object or array with required keys
                        if (is_object($option)) {
                            if (!isset($option->name) || !isset($option->value)) {
                                $fail("The {$attribute}.{$index} must have both 'name' and 'value' properties.");
                            }
                        } elseif (is_array($option)) {
                            if (!array_key_exists('name', $option) || !array_key_exists('value', $option)) {
                                $fail("The {$attribute}.{$index} must have both 'name' and 'value' keys.");
                            }
                        } else {
                            $fail("The {$attribute}.{$index} must be an object or associative array.");
                        }
                    }
                },
            ],
        ];
    }
}
?>

<div>
    <flux:field class="flex justify-between">
        <flux:label>
            {{ $label }}
        </flux:label>
        <flux:select wire:model="value"
                     placeholder="{{ $placeholder }}"
                     class="!w-1/2">
            @foreach ($options as $option)
                @if (is_object($option) && isset($option->name, $option->value))
                    <flux:select.option value="{{ $option->name }}">
                        {{ $option->value }}
                    </flux:select.option>
                @endif
            @endforeach
        </flux:select>
    </flux:field>
</div>
