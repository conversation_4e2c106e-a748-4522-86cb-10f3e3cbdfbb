<?php

use Livewire\Attributes\Modelable;
use Livewire\Attributes\Validate;
use Livewire\Volt\Component;

new class extends Component {
    #[Modelable]
    public ?string $value;

    public string $label = '';
    public string $placeholder = '';
    #[Validate([
        'options' => 'required',
        'options.*.name' => 'required',
        'options.*.value' => 'required',
    ])]
    public array $options = [];
}
?>

<div>
    <flux:field class="flex justify-between">
        <flux:label>
            {{ $label }}
        </flux:label>
        <flux:select wire:model="value"
                     placeholder="{{ $placeholder }}"
                     class="!w-1/2">
            @foreach ($options as $option)
                @if (is_object($option) && isset($option->name, $option->value))
                    <flux:select.option value="{{ $option->name }}">
                        {{ $option->value }}
                    </flux:select.option>
                @endif
            @endforeach
        </flux:select>
    </flux:field>
</div>
