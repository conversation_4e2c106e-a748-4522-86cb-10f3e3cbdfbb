<?php

use Livewire\Attributes\Modelable;
use Livewire\Volt\Component;

new class extends Component
{
    #[Modelable]
    public ?string $value;

    public string $label = '';
    public string $placeholder = '';
    public array $options = [];
}
?>

<div>
    <flux:field class="flex justify-between">
        <flux:label>
            {{ $label }}
        </flux:label>
        <flux:select wire:model="value"
                     placeholder="{{ $placeholder }}"
                     class="!w-1/2">
            @foreach ($options as $option)
                <flux:select.option value="{{ $option->name }}">
                    {{ $option->value }}
                </flux:select.option>
            @endforeach
        </flux:select>
    </flux:field>
</div>
