<?php

use Livewire\Volt\Component;
use Livewire\Attributes\Modelable;

new class extends Component
{
    #[Modelable]
    public ?string $value;

    public string $label = '';
    public string $placeholder = '';
    public array $options = [];

}; ?>

<div>
    <flux:field class="flex justify-between">
        <flux:label>
            {{ $label }}
        </flux:label>
        <flux:select wire:model="value"
                     placeholder="{{ $placeholder }}"
                     class="!w-1/2">
            @foreach ($options as $option)
                @if (is_object($option) && isset($option->name, $option->value))
                    <flux:select.option value="{{ $option->name }}">
                        {{ $option->value }}
                    </flux:select.option>
                @endif
            @endforeach
        </flux:select>
    </flux:field>
</div>
