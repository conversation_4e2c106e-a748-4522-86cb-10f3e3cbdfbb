<?php

use function Livewire\Volt\state;

state('value')->modelable();
state([
    'label' => '',
    'placeholder' => '',
    'options' => [],
]);

?>

<div>
    <flux:field class="flex justify-between">
        <flux:label>
            {{ $label }}
        </flux:label>
        <flux:select wire:model="value"
                     placeholder="{{ $placeholder }}"
                     class="!w-1/2">
            @foreach ($options as $option)
                @if (is_object($option) && isset($option->name, $option->value))
                    <flux:select.option value="{{ $option->name }}">
                        {{ $option->value }}
                    </flux:select.option>
                @endif
            @endforeach
        </flux:select>
    </flux:field>
</div>
