<?php

use Livewire\Attributes\Modelable;
use Livewire\Volt\Component;

new class extends Component
{
    #[Modelable]
    public ?string $value;

    public string $label = '';
    public string $placeholder = '';
};

?>

<flux:field class="flex justify-between">
    <flux:label>
        {{ $label }}
    </flux:label>
    <flux:input type="text" wire:model="value" class="!w-1/3" placeholder="{{ $placeholder }}"/>
</flux:field>
