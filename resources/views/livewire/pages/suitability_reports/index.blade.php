<?php

use App\Domains\SuitabilityReports\Models\SuitabilityReportCustomer;
use Flux\Flux;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Pagination\LengthAwarePaginator;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;

new #[Layout('layouts.app')] class extends Component
{
    use \Livewire\WithPagination;

    /**
     * The search term for the customer name.
     */
    #[Url]
    public ?string $search = null;

    /**
     * The selected column for sorting by.
     */
    #[Url]
    public string $sortBy = 'updated_at';

    /**
     * The direction for sorting.
     */
    #[Url]
    public string $sortDirection = 'desc';

    #[Computed]
    public function customers(): LengthAwarePaginator
    {
        return SuitabilityReportCustomer::query()
            ->with(['estatePlanner' => fn (BelongsTo $query): BelongsTo => $query->select(['id', 'name'])])
            ->when($this->search, fn (Builder $query, string $search): Builder => $query->whereLike('name', "%{$search}%"))
            ->when($this->sortBy, fn (Builder $query, string $sortBy): Builder => $query->orderBy($sortBy, $this->sortDirection))
            ->select(['id', 'name', 'hubspot_id', 'estate_planner_id', 'updated_at'])
            ->paginate(20);
    }

    /**
     * Sort the suitability report customers by the given column.
     */
    public function sort($column): void
    {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function openHubspotLink(?string $hubspot_id): void
    {
        if ($hubspot_id) {

            $hubspotUrl = 'https://app.hubspot.com/contacts/' . config('services.hubspot.portal_id') . "/record/0-1/{$hubspot_id}";

            $this->js("window.open('{$hubspotUrl}', '_blank')");

        } else {
            Flux::toast(
                text: 'This customer has no hubspot id',
                variant: 'warning',
            );
        }
    }
};

?>

<x-slot name="header" wire:model="search">
    <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 flex gap-x-2 dark:text-gray-200 leading-tight">
            <flux:icon name="clipboard-document-list"/>
            {{ __('Internal Suitability Reports') }}
        </h2>
        <livewire:pages.suitability_reports.partials.create/>
    </div>
</x-slot>
<div class="flex flex-col gap-5 max-w-7xl mx-auto p-6">
    {{-- SuitabilityReportCustomer search  --}}
    <flux:input wire:model.live.debounce.500ms="search" placeholder="Search customer name here..."
                icon="magnifying-glass"
                class="self-end max-w-1/3"/>
    {{-- SuitabilityReportCustomer list --}}
    <x-flux::card>
        <flux:table :paginate="$this->customers()">
            <flux:table.columns>
                <flux:table.column>
                    {{ __('Name') }}
                </flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'estate_planner_id'" :direction="$sortDirection"
                                   wire:click="sort('estate_planner_id')">
                    {{ __('Estate Planner') }}
                </flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'updated_at'" :direction="$sortDirection"
                                   wire:click="sort('updated_at')">
                    {{ __('Last Updated') }}
                </flux:table.column>
                <flux:table.column/>
            </flux:table.columns>
            <flux:table.rows>
                @foreach ($this->customers() as $customer)
                    <flux:table.row :key="$customer->id">
                        <flux:table.cell>
                            {{ $customer->name }}
                        </flux:table.cell>
                        <flux:table.cell>
                            {{ $customer->estatePlanner->name ?? __('No estate planner') }}
                        </flux:table.cell>
                        <flux:table.cell>
                            {{ $customer->updated_at }}
                        </flux:table.cell>
                        <flux:table.cell class="flex items-center">
                            <flux:spacer/>
                            <flux:dropdown>
                                <flux:button square>...</flux:button>
                                <flux:menu>
                                    <flux:navmenu.item icon="eye"
                                                       :href="route('suitability.show', $customer->id)">View
                                    </flux:navmenu.item>
                                    <flux:menu.separator/>
                                    <flux:menu.item wire:click="openHubspotLink('{{ $customer->hubspot_id }}')"
                                                    icon="square-arrow-out-up-right">
                                        Hubspot
                                    </flux:menu.item>
                                </flux:menu>

                            </flux:dropdown>

                        </flux:table.cell>
                    </flux:table.row>
                @endforeach
            </flux:table.rows>
        </flux:table>
    </x-flux::card>
</div>
