<?php

use App\Domains\SuitabilityReports\Models\SuitabilityReportCustomer;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Computed;
use Livewire\Features\SupportRedirects\Redirector;
use Livewire\Volt\Component;

new class extends Component
{
    /**
     * The customer name for the new suitability report.
     */
    public string $customerName = '';

    #[Computed]
    public function disabled(): bool
    {
        return $this->customerName === '';
    }

    /**
     * Create a new customer and redirect to the ISR form.
     */
    public function create(): Redirector
    {
        $customer = SuitabilityReportCustomer::create([
            'name' => $this->customerName,
            'estate_planner_id' => Auth::id(),
        ]);

        return redirect()->route('suitability.show', $customer->id);
    }
};
?>

<div>
    <flux:modal.trigger name="create-isr">
        <flux:button variant="primary" icon="pencil" inset="top bottom">
            {{ __('Create') }}
        </flux:button>
    </flux:modal.trigger>
    <flux:modal name="create-isr" class="w-96">
        <div class="space-y-6">
            <div class="space-y-2">
                <flux:heading size="lg">Create an Internal Suitability Report</flux:heading>
                <flux:description>Enter the customer's name to begin a new ISR</flux:description>
            </div>
            <div class="flex gap-x-2">
                <flux:input name="customer_name" wire:model.live.debounce.250ms="customerName"
                    placeholder="Enter customer name here..." autofocus="true" />
                <flux:button variant="primary" class="place-self-end" wire:click="create" :disabled="$this->disabled()">
                    {{ __('Create') }}
                </flux:button>
            </div>
        </div>
    </flux:modal>
</div>
