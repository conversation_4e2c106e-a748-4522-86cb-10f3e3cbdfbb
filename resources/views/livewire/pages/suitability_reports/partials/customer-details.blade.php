<?php

use App\Domains\SuitabilityReports\Models\SuitabilityReportCustomer;
use Illuminate\Support\Carbon;
use Livewire\Volt\Component;
use Livewire\Attributes\Modelable;

new class extends Component
{
    #[Modelable]
    public SuitabilityReportCustomer $customer;

    public string $customer_name = '';
    public string $customer_dob = '';
    public string $estate_planner_name = '';
    public string $class = '';

    public function mount(): void
    {
        $this->customer_name = $this->customer->name;
        $this->customer_dob = $this->customer->date_of_birth;
        $this->estate_planner_name = $this->customer->estatePlanner?->name ?? __('No estate planner');
    }

    public function updatedCustomerName($value): void
    {
        $this->customer->update([
            'name' => $value,
        ]);
    }

    public function updatedCustomerDob($value): void
    {
        $this->customer->update([
            'date_of_birth' => $value ? Carbon::parse($value) : null,
        ]);
    }
}; ?>

<div class="{{ $class }}">
    <div class=" flex items-center justify-between">
        <flux:heading size="xl" class=" ml-2 mb-4 w-full">
            {{ __('Customer Details') }}
        </flux:heading>
    </div>
    <x-flux::card class="flex justify-between gap-4">
        <div class="flex gap-x-2">
            <flux:input label="Customer" wire:model.live.debounce="customer_name"
                        placeholder="Enter the customer's name"/>
            <flux:date-picker selectable-header wire:model.live.debounce="customer_dob" label="Customer DOB">
                <x-slot name="trigger">
                    <flux:date-picker.input/>
                </x-slot>
            </flux:date-picker>
            <flux:input :value="$this->customer->age()" label="Age" class="max-w-12" disabled/>
        </div>
        <div class="flex flex-col items-end gap-2">
            <flux:label for="estate_planner_id" class="!m-0">
                {{ __('Estate Planner') }}
            </flux:label>
            <flux:description>
                {{ $this->estate_planner_name }}
            </flux:description>
        </div>
    </x-flux::card>
</div>
