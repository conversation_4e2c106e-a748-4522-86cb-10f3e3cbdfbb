<?php

use App\Domains\SuitabilityReports\Forms\SuitabilityReportForm;
use Livewire\Attributes\Modelable;
use Livewire\Volt\Component;

new class extends Component
{
    #[Modelable]
    public SuitabilityReportForm $form;

    public function updatedFormLpa($value): void
    {
        $this->dispatch('has_lpa_updated');
    }
}
?>

<div>
    <flux:heading size="xl" class=" ml-2 w-full mb-4"> {{ __("Estate Planning") }} </flux:heading>
    <flux:card class="px-12 py-14">
        <livewire:pages.suitability_reports.components.radio-group wire:model.change="form.current_will"
                                                                   :label="__('Do they currently have a will?')"
                                                                   :suggestion="__('We recommend that everyone has a will')"
        />
            <div x-show="$wire.form.current_will" class="mt-4 ml-8">
                <livewire:pages.suitability_reports.components.radio-group wire:model.change="form.current_will_has_trust"
                                                                           :label="__('Does their current will have a trust?')"
                />
            </div>

        <flux:separator class="my-6"/>
        <livewire:pages.suitability_reports.components.radio-group wire:model.change="form.lpa"
                                                                   :label="__('Do they have Lasting Power of Attorney (LPAs)?')"
                                                                   :suggestion="__('We recommend that everyone sorts their LPAs')"
        />
    </flux:card>
</div>
