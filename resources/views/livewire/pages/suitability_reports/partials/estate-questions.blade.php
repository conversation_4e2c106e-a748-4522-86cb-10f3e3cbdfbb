<?php

use App\Domains\SuitabilityReports\Enums\EstateValue;
use App\Domains\SuitabilityReports\Forms\SuitabilityReportForm;
use Livewire\Attributes\Modelable;
use Livewire\Volt\Component;

new class extends Component
{
    #[Modelable]
    public SuitabilityReportForm $form;

    public bool $show_value_of_estate_iht = false;
    public array $estate_value_options = [];

    public function mount(): void
    {
        $this->estate_value_options = EstateValue::cases();
    }

    public function updatedFormNumberOfProperties($value): void
    {
        $this->form->number_of_properties = $value ? 1 : null;
    }

    public function updatedFormValueOfEstate($value): void
    {
        $this->show_value_of_estate_iht = in_array($value, [
            EstateValue::OVER_2MILLION->name,
            EstateValue::BETWEEN_1_AND_2_MILLION->name,
        ]);
        $this->dispatch('estate_value_updated');
    }
}; ?>

<div>
    <flux:heading size="xl" class=" ml-2 w-full mb-4"> {{ __("Estate") }} </flux:heading>
    <flux:card class="px-12 py-14">
        <livewire:pages.suitability_reports.components.radio-group wire:model.live="form.property"
                                                                   :label="__('Do they have property?')"
        />
        @if ($this->form->property)
            <livewire:pages.suitability_reports.components.number-input wire:model.change="form.number_of_properties"
                                                                        :label="__('Number of properties')"
                                                                        :placeholder="__('Enter number of properties')"
            />
            <livewire:pages.suitability_reports.components.customer-response-select
                wire:model.change="form.property_ownership_iht"
                :label="__('The rules around inheritance tax are complicated but people who own property may benefit from understanding how inheritance tax will effect the division of their estate. Are you interested in discussing this?')"
                :placeholder="__('Select response')"
                class="mt-6"
            />
        @endif
        <flux:separator class="my-6"/>
        <livewire:pages.suitability_reports.components.radio-group wire:model.change="form.own_a_business"
                                                                   :label="__('Do they own a business?')"
        />
        @if ($this->form->own_a_business)
            <livewire:pages.suitability_reports.components.customer-response-select
                wire:model.change="form.business_ownership_iht"
                :label="__('The rules around inheritance tax are complicated but some business owners may benefit from understanding how inheritance tax will effect the division of their estate. Are you interested in discussing this?')"
                :placeholder="__('Select response')"
                class="mt-6"
            />
        @endif
        <flux:separator class="my-6"/>
        <livewire:pages.suitability_reports.components.select wire:model.change="form.value_of_estate"
                                                              :label="__('What is the value of their estate?')"
                                                              :options="$estate_value_options"
                                                              :placeholder="__('Enter the value of their estate...')"
        />
        @if ($this->show_value_of_estate_iht)
            <livewire:pages.suitability_reports.components.customer-response-select
                wire:model.change="form.value_of_estate_iht"
                :label="__('The rules around inheritance tax are complicated but due to the value of your estate you could benefit from a conversation about how inheritance tax will effect the division of their estate. Is this something you’re interested in?')"
                :placeholder="__('Select response')"
                class="mt-6"
            />
        @endif
    </flux:card>
</div>

