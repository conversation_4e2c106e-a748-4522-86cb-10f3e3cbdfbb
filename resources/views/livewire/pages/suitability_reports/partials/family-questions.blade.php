<?php

use App\Domains\SuitabilityReports\Enums\RelationshipStatus;
use App\Domains\SuitabilityReports\Forms\SuitabilityReportForm;

use function Livewire\Volt\mount;
use function Livewire\Volt\state;
use function Livewire\Volt\updated;

state('form')->type(SuitabilityReportForm::class)->modelable();

state([
    'show_relationship_questions' => false,
    'relationship_status_options' => [],
    'show_divorce_protection_questions' => false,
    'show_married_with_children_life_trust_question' => false,
]);

mount(function () {
    $this->relationship_status_options = RelationshipStatus::cases();
});

updated([
    'form.relationship_status' => function ($value) {
        $this->show_relationship_questions = in_array($value, [
            RelationshipStatus::MARRIED_OR_PARTNER->name,
            RelationshipStatus::ENGAGED->name,
        ]);

        $this->show_divorce_protection_questions = $value === RelationshipStatus::DIVORCED_OR_SEPERATED->name;

        $this->show_married_with_children_life_trust_question = $value === RelationshipStatus::MARRIED_OR_PARTNER->name && $this->form->children;

        $this->has_partner = $this->show_relationship_questions;
        $this->dispatch('has_partner_updated');
    },
    'form.children' => function ($value) {
        $this->form->number_of_children = $value ? 1 : null;
        $this->show_married_with_children_life_trust_question = $this->relationship_status === RelationshipStatus::MARRIED_OR_PARTNER->name && $value;
        $this->dispatch('has_children_updated');
    },
]);

?>

<div>
    <flux:heading size="xl" class=" ml-2 w-full mb-4"> {{ __("Family") }} </flux:heading>
    <flux:card class="px-12 py-14">
        <livewire:pages.suitability_reports.components.select wire:model.change="form.relationship_status"
                                                              :label="__('Relationship status')"
                                                              :options="$relationship_status_options"
                                                              :placeholder="__('Select relationship status')"
        />
        @if ($this->show_divorce_protection_questions)
            <livewire:pages.suitability_reports.components.customer-response-select
                wire:model.change="form.life_interest_trust"
                :label="__('A Life Interest Trust is a good way to protect the distribution of your estate. For example, protecting inheritance against remarriage or allowing your partner to benefit whilst they are still alive before passing to children.')"
                :placeholder="__('Select response')"
                class="mt-6"
            />
        @endif
        @if ($this->show_relationship_questions)
            <div class="flex flex-col gap-y-6 mt-4 ml-8">
                <livewire:pages.suitability_reports.components.text-input wire:model.change="form.partner_name"
                                                                          :label="__('Partner Name')"
                                                                          :placeholder="__('Enter partner name')"
                />
                <livewire:pages.suitability_reports.components.dob-input wire:model.change="form.partner_dob"
                                                                         :label="__('Partner DOB')"
                                                                         :placeholder="__('DD/MM/YYYY')"
                />
                <livewire:pages.suitability_reports.components.radio-group wire:model.change="form.partner_has_will"
                                                                           :label="__('Does their partner have a will?')"
                                                                           :suggestion="__('We recommend that everyone has a will')"
                />
                <livewire:pages.suitability_reports.components.radio-group wire:model.change="form.partner_has_lpa"
                                                                           :label="__('Does their partner have Lasting Powers of Attorney?')"
                                                                           :suggestion="__('We recommend that everyone sorts their LPAs')"
                />

            </div>

        @endif
        <flux:separator class="my-6"/>
        <livewire:pages.suitability_reports.components.radio-group wire:model.change="form.children"
                                                                   :label="__('Do they have children?')"/>
        @if ($this->form->children)
            <div class="flex flex-col gap-y-6 mt-4 ml-8">
                <livewire:pages.suitability_reports.components.number-input wire:model.change="form.number_of_children"
                                                                            :label="__('Number of children')"
                                                                            :placeholder="__('Enter number of children')"
                />
                @if ($this->show_married_with_children_life_trust_question)
                    <livewire:pages.suitability_reports.components.customer-response-select
                        wire:model.change="form.married_with_children_life_trust"
                        :label="__('A Life Interest Trust is a good way to protect the distribution of your estate between your partner and children. For example, protecting inheritance against remarriage or allowing your partner to benefit whilst they are still alive before passing to children. ')"
                        :placeholder="__('Select response')"
                    />
                @endif

                <livewire:pages.suitability_reports.components.radio-group wire:model.change="form.children_under_18"
                                                                           :label="__('Are their children under 18?')"
                />
                @if ($this->form->children_under_18)
                    <livewire:pages.suitability_reports.components.customer-response-select
                        wire:model.change="form.children_under_18_will_trust"
                        :label="__('The legal age of inheritance is 18, many people feel this is too young for a child to inherit. You can state any age up to 25. Would you prefer them to inherit at a later age?')"
                        :placeholder="__('Select response')"
                    />
                @endif

                <livewire:pages.suitability_reports.components.radio-group wire:model.change="form.step_children"
                                                                           :label="__('Do they have any step-children?')"
                />
                @if ($this->form->step_children)
                    <livewire:pages.suitability_reports.components.customer-response-select
                        wire:model.change="form.step_family_life_trust"
                        :label="__('If you have concerns about the competing interests of your own children versus step-children, you could benefit from a Life Interest Trust to ring-fence where you want your assets to go eventually, whilst providing for a spouse/parter during their lifetime.')"
                        :placeholder="__('Select response')"
                    />
                @endif
                <livewire:pages.suitability_reports.components.radio-group
                    wire:model.change="form.children_with_disability_or_care_needs"
                    :label="__('Children with disability or care needs?')"
                />
                @if ($this->form->children_with_disability_or_care_needs)
                    <livewire:pages.suitability_reports.components.customer-response-select
                        wire:model.change="form.child_with_disability_trust"
                        :label="__('A Disabled Person’s trust can help support your child financially without affecting their benefits. The money is managed by people who you choose and will be used for their care.')"
                        :placeholder="__('Select response')"
                    />
                @endif
            </div>
        @endif
        <flux:separator class="my-6"/>

        <div class="flex justify-between">
            <flux:heading size="sm"> {{ __("Do they have a partner") }} </flux:heading>
            <flux:text>{{ $this->form->has_partner ? __("Yes") : __("-") }}</flux:text>
        </div>
    </flux:card>
</div>


