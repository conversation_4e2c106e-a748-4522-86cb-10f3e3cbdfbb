<?php

use App\Domains\SuitabilityReports\DTOs\FieldData;
use App\Domains\SuitabilityReports\Forms\SuitabilityReportForm;
use Livewire\Attributes\Locked;
use Livewire\Attributes\Modelable;
use Livewire\Volt\Component;

new class extends Component
{
    /**
     * The livewire key for the field.
     */
    #[Locked]
    public string $key;

    /**
     * The form model reference.
     */
    #[Modelable]
    public SuitabilityReportForm $form;

    /**
     * The field data for the component.
     */
    #[Locked]
    public FieldData $field;

    /**
     * Any classes to apply to the component.
     */
    #[Locked]
    public ?string $class;
};
?>

<flux:field name="{{ $field->name }}" class="flex items-center justify-between py-2 {{ $class }}">
    <flux:label @class([
        '!m-0 w-60',
        '!text-green-300' => $field->isFlagQuestion && $form->{$field->name},
        '!text-orange-300' => $field->isFlagQuestion && $form->{$field->name} === null,
    ])>
        {{ $field->label }}
        @if ($field->required)
            <span class="text-red-600">{{ "*" }}</span>
        @endif
    </flux:label>
    @if ($field->description)
        <flux:description class="text-sm text-gray-500">{{ $field->description }}</flux:description>
    @endif
    @if ($field->input[0] === 'input')
        @if ($field->input[1] === 'number')
            <flux:input wire:model.number="form.{{ $field->name }}" :key="$key"
                        placeholder="{{ $field->placeholder }}" class="!w-1/2" type="number"
                        required="{{ $field->required }}"/>
        @else
            <flux:input wire:model="form.{{ $field->name }}" :key="$key"
                        placeholder="{{ $field->placeholder }}" class="!w-1/2" required="{{ $field->required }}"/>
        @endif
    @elseif ($field->input[0] === 'select')
        <flux:select wire:model.live="form.{{ $field->name }}" :key="$key"
                     placeholder="{{ $field->placeholder }}" class="!w-1/2">
            @foreach ($field->input[1] as $option)
                <flux:select.option value="{{ $option->name }}">
                    {{ $option->value }}
                </flux:select.option>
            @endforeach
        </flux:select>
    @elseif ($field->input[0] === 'boolean')
        <flux:checkbox wire:model="form.{{ $field->name }}" :key="$key"/>
    @elseif ($field->input[0] === 'text')
        <flux:description class="text-sm text-gray-500">
            {{ $form->has_partner ? $field->input[1][0]->name : $field->placeholder }}
        </flux:description>
    @endif
</flux:field>
