<?php

use function Livewire\Volt\state;

state([
    'marketingPreferences' => '',
]);

\Livewire\Volt\updated([
    'marketingPreferences' => function ($value) {
        ray($this->marketingPreferences)->label('Marketing Preferences');
    },
]);

?>
<div>
    <flux:heading size="xl" class=" ml-2 w-full mb-4"> {{ __("Marketing Preferences") }} </flux:heading>
    <flux:card class=" flex flex-col gap-y-4 px-12 py-14">
        <flux:field class="flex justify-between">
            <flux:label for="current_will">
                {{ __("Yes to all") }}
            </flux:label>
            <flux:checkbox wire:model.change="marketingPreferences" value="all"/>
        </flux:field>
        <flux:field class="flex justify-between">
            <flux:label for="current_will">
                {{ __("Email or Phone") }}
            </flux:label>
            <flux:checkbox wire:model.change="marketingPreferences" value="email-or-phone"/>
        </flux:field>
        <flux:field class="flex justify-between">
            <flux:label for="current_will">
                {{ __("Phone only") }}
            </flux:label>
            <flux:checkbox wire:model.change="marketingPreferences" value="phone"/>
        </flux:field>
        <flux:field class="flex justify-between">
            <flux:label for="current_will">
                {{ __("Email only") }}
            </flux:label>
            <flux:checkbox wire:model.change="marketingPreferences" value="email"/>
        </flux:field>
        <flux:field class="flex justify-between">
            <flux:label for="current_will">
                {{ __("None") }}
            </flux:label>
            <flux:checkbox wire:model.change="marketingPreferences" value="none"/>
        </flux:field>
    </flux:card>
</div>

