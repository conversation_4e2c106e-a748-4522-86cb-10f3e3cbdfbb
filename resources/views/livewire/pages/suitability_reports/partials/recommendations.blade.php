<?php

use App\Domains\SuitabilityReports\Enums\EstateValue;
use App\Domains\SuitabilityReports\Forms\SuitabilityReportForm;
use Livewire\Attributes\Modelable;
use Livewire\Attributes\On;
use Livewire\Volt\Component;

new class extends Component
{
    public string $class = '';

    #[Modelable]
    public SuitabilityReportForm $form;

    #[On('has_partner_updated')]
    #[On('has_lpa_updated')]
    public function products(): string
    {
        return ($this->form->lpa ? 'Will ' : 'Will + LPA ') . ($this->form->has_partner ? '(couple)' : '(single)');
    }

    #[On('estate_value_updated')]
    public function iht(): string
    {
        return in_array($this->form->value_of_estate, [EstateValue::BETWEEN_1_AND_2_MILLION->name, EstateValue::OVER_2MILLION->name]) ? 'IHT Recommended' : 'IHT Not recommended';
    }

    #[On('has_children_updated')]
    public function trusts(): string
    {
        $recommended = $this->form->children || $this->form->step_children || $this->form->children_with_disability_or_care_needs;
        $notRecommended = ! $this->form->children && ! $this->form->step_children && ! $this->form->children_with_disability_or_care_needs;

        if ($recommended) {
            return 'Recommended';
        }

        if ($notRecommended) {
            return 'Not Recommended';
        }

        return '';
    }
}

?>
<div class="{{ $class }}">
    <flux:heading size="xl" class="mb-4">
        {{ __('Recommendations') }}
    </flux:heading>
    <x-flux::card class="col-start-2 flex flex-col gap-4 h-fit">
        <flux:heading>{{ __('Products') }}</flux:heading>
        <flux:text>{{ $this->products() }}</flux:text>
        <flux:heading>{{ __('Trusts') }}</flux:heading>
        <flux:text>{{ $this->trusts() }}</flux:text>
        <flux:heading>{{ __('IHT') }}</flux:heading>
        <flux:text>{{ $this->iht() }}</flux:text>
    </x-flux::card>
</div>
