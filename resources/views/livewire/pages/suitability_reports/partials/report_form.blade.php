<?php

use App\Domains\SuitabilityReports\DTOs\BooleanAnswer;
use App\Domains\SuitabilityReports\DTOs\FieldData;
use App\Domains\SuitabilityReports\Enums\CustomerInterestResponses;
use App\Domains\SuitabilityReports\Enums\EstateValue;
use App\Domains\SuitabilityReports\Enums\RelationshipStatus;
use App\Domains\SuitabilityReports\Forms\SuitabilityReportForm;
use App\Domains\SuitabilityReports\Models\SuitabilityReportCustomer;
use Livewire\Attributes\Locked;
use Livewire\Attributes\Modelable;
use Livewire\Volt\Component;

new class extends Component
{
    /**
     * The list of fields.
     *
     * @var array<FieldData>
     */
    #[Locked]
    public array $fields;

    #[Locked]
    public SuitabilityReportCustomer $customer;

    #[Modelable]
    public SuitabilityReportForm $form;

    public function mount(): void
    {
        $yesNoOptions = [new BooleanAnswer(name: 'Yes', value: 1), new BooleanAnswer(name: 'No', value: 0)];

        $this->fields = [
            new FieldData(name: 'relationship_status', label: 'Relationship status', input: ['select', RelationshipStatus::cases()], required: true, placeholder: 'Choose relationship status...', check: [RelationshipStatus::ENGAGED->name, RelationshipStatus::MARRIED_OR_PARTNER->name, RelationshipStatus::DIVORCED_OR_SEPERATED->name], subQuestions: [
                new FieldData(name: 'partner_name', label: 'Partner name', input: ['input', 'text'], placeholder: 'Enter partner name...', shouldDisplay: $this->form->relationship_status === 'ENGAGED' || $this->form->relationship_status === 'MARRIED_OR_PARTNER'),
                new FieldData(name: 'life_interest_trust', label: 'Remarriage: Life interest trust', input: ['select', CustomerInterestResponses::cases()], placeholder: 'Customers response...', check: true, shouldDisplay: $this->form->relationship_status === 'DIVORCED_OR_SEPERATED', isFlagQuestion: true),
                new FieldData(name: 'married_with_children_life_trust', label: 'Married with children: Life interest trust to protect against remarriage', input: ['select', CustomerInterestResponses::cases()], placeholder: 'Customers response...', check: true, shouldDisplay: $this->form->relationship_status === 'MARRIED_OR_PARTNER' && $this->form->children, isFlagQuestion: true),
            ]),

            new FieldData(name: 'children', label: 'Whether they have children', input: ['boolean', $yesNoOptions], placeholder: 'Choose whether they have children...', check: true, subQuestions: [
                new FieldData(name: 'number_of_children', label: 'Number of children', input: ['input', 'number'], placeholder: 'Enter number of children...'),
                new FieldData(name: 'children_under_18', label: 'Children under 18', input: ['boolean', $yesNoOptions], placeholder: 'Choose whether they have children under 18...'),
                new FieldData(name: 'children_under_18_will_trust', label: 'Children under 18: Will with trust (inheritance age)', input: ['select', CustomerInterestResponses::cases()], placeholder: 'Customers response...', check: true, shouldDisplay: $this->form->children_under_18, isFlagQuestion: true),
                new FieldData(name: 'step_children', label: 'Step-children', input: ['boolean', $yesNoOptions], placeholder: 'Choose whether they have step_children...'),
                new FieldData(name: 'step_family_life_trust', label: 'Step-family: Life interest trust due to remarriage', input: ['select', CustomerInterestResponses::cases()], placeholder: 'Customers response...', check: true, shouldDisplay: $this->form->step_children, isFlagQuestion: true),
                new FieldData(name: 'children_with_disability_or_care_needs', label: 'Children with disability or care needs', input: ['boolean', $yesNoOptions], placeholder: 'Choose whether they have children with disability or care needs...'),
                new FieldData(name: 'child_with_disability_trust', label: 'Child with disability: Disabled persons trust', input: ['select', CustomerInterestResponses::cases()], placeholder: 'Customers response...', check: true, shouldDisplay: $this->form->children_with_disability_or_care_needs, isFlagQuestion: true),
            ]),

            new FieldData(name: 'property', label: 'Whether they have property', input: ['boolean', $yesNoOptions], placeholder: 'Choose whether they have property...', check: true, subQuestions: [
                new FieldData(name: 'number_of_properties', label: 'Number of properties', input: ['input', 'number'], placeholder: 'Enter number of properties...'),
                new FieldData(name: 'property_ownership_iht', label: 'Property ownership: Consider IHT (for some, not all)', input: ['select', CustomerInterestResponses::cases()], placeholder: 'Customers response...', check: true, shouldDisplay: $this->form->property, isFlagQuestion: true),
            ]),

            new FieldData(name: 'own_a_business', label: 'Whether they own a business', input: ['boolean', $yesNoOptions], placeholder: 'Choose whether they own a business...', check: true, subQuestions: [
                new FieldData(name: 'business_ownership_iht', label: 'Business ownership: Consider IHT (for some, not all)', input: ['select', CustomerInterestResponses::cases()], placeholder: 'Customers response...', check: true, shouldDisplay: $this->form->own_a_business, isFlagQuestion: true),
            ]),

            new FieldData(name: 'value_of_estate', label: 'The value of their estate', input: ['select', EstateValue::cases()], placeholder: 'Enter the value of their estate...', check: [EstateValue::OVER_2MILLION->name, EstateValue::BETWEEN_1_AND_2_MILLION->name], subQuestions: [
                new FieldData(name: 'value_of_estate_iht', label: 'IHT Recommended', input: ['select', CustomerInterestResponses::cases()], placeholder: 'Customers response...', check: true, shouldDisplay: $this->form->value_of_estate === EstateValue::OVER_2MILLION->name || $this->form->value_of_estate === EstateValue::BETWEEN_1_AND_2_MILLION->name, isFlagQuestion: true),
            ]),

            new FieldData(name: 'current_will', label: 'Whether they currently have a will', input: ['boolean', $yesNoOptions], placeholder: 'Choose whether they currently have a will...', check: true, subQuestions: [new FieldData(name: 'current_will_has_trust', label: 'Whether their current will has a trust', input: ['boolean', $yesNoOptions], placeholder: 'Choose whether their current will has a trust...')]),
            new FieldData(name: 'lpa', label: 'Whether they have an LPA', input: ['boolean', $yesNoOptions], placeholder: 'Choose whether they have an LPA...', check: true),
            new FieldData(name: 'has_partner', label: 'Whether they have a partner', input: ['text', $yesNoOptions], placeholder: '-', check: true, subQuestions: [
                new FieldData(name: 'partner_has_will', label: 'If partner has a will', input: ['boolean', $yesNoOptions], placeholder: 'Choose whether partner has a will...'),
                new FieldData(name: 'partner_will', label: 'We recommend a will', input: ['select', CustomerInterestResponses::cases()], placeholder: 'Customers response...', check: true, shouldDisplay: $this->form->has_partner && ! $this->form->partner_has_will, isFlagQuestion: true),
                new FieldData(name: 'partner_has_lpa', label: 'If partner has an LPA', input: ['boolean', $yesNoOptions], placeholder: 'Choose whether partner has an LPA...'),
                new FieldData(name: 'partner_lpa', label: 'We recommend an LPA', input: ['select', CustomerInterestResponses::cases()], placeholder: 'Customers response...', check: true, shouldDisplay: $this->form->has_partner && ! $this->form->partner_has_lpa, isFlagQuestion: true),

            ]),
            new FieldData(name: 'marketing_preferences', label: 'Marketing preferences', input: ['boolean', $yesNoOptions], placeholder: 'Choose marketing preferences...', check: true),
        ];
    }

    public function updated(string $property): void
    {
        switch ($property) {
            case 'form.relationship_status':
                $this->form->has_partner = $this->form->relationship_status === RelationshipStatus::ENGAGED->name || $this->form->relationship_status === RelationshipStatus::MARRIED_OR_PARTNER->name;
                $this->fields[0]->subQuestions[0]->setShouldDisplay($this->form->relationship_status === 'ENGAGED' || $this->form->relationship_status === 'MARRIED_OR_PARTNER');
                $this->fields[0]->subQuestions[1]->setShouldDisplay($this->form->relationship_status === 'DIVORCED_OR_SEPERATED');
                $this->fields[0]->subQuestions[2]->setShouldDisplay($this->form->relationship_status === 'MARRIED_OR_PARTNER' && $this->form->children);
                $this->fields[7]->subQuestions[1]->setShouldDisplay($this->form->has_partner && ! $this->form->partner_has_will);
                $this->fields[7]->subQuestions[3]->setShouldDisplay($this->form->has_partner && ! $this->form->partner_has_lpa);
                $this->dispatch('has_partner_updated');
                break;
            case 'form.children':
                $this->form->number_of_children = $this->form->children ? 1 : null;
                $this->fields[0]->subQuestions[2]->setShouldDisplay($this->form->relationship_status === 'MARRIED_OR_PARTNER' && $this->form->children);
                $this->dispatch('has_children_updated');
                break;
            case 'form.number_of_children':
                if ($this->form->number_of_children !== null && $this->form->number_of_children < 1) {
                    $this->form->number_of_children = 1;
                }
                break;
            case 'form.children_under_18':
                $this->fields[1]->subQuestions[2]->setShouldDisplay($this->form->children_under_18);
                break;
            case 'form.step_children':
                $this->fields[1]->subQuestions[4]->setShouldDisplay($this->form->step_children);
                break;
            case 'form.children_with_disability_or_care_needs':
                $this->fields[1]->subQuestions[6]->setShouldDisplay($this->form->children_with_disability_or_care_needs);
                break;
            case 'form.property':
                $this->fields[2]->subQuestions[1]->setShouldDisplay($this->form->property);
                break;
            case 'form.own_a_business':
                $this->fields[3]->subQuestions[0]->setShouldDisplay($this->form->own_a_business);
                break;
            case 'form.value_of_estate':
                $this->fields[4]->subQuestions[0]->setShouldDisplay($this->form->value_of_estate === EstateValue::BETWEEN_1_AND_2_MILLION->name || $this->form->value_of_estate === EstateValue::OVER_2MILLION->name);
                $this->dispatch('estate_value_updated');
                break;
            case 'form.lpa':
                $this->dispatch('has_lpa_updated');
                break;
            case 'form.partner_has_will':
                $this->fields[7]->subQuestions[1]->setShouldDisplay($this->form->has_partner && ! $this->form->partner_has_will);
                break;
            case 'form.partner_has_lpa':
                $this->fields[7]->subQuestions[3]->setShouldDisplay($this->form->has_partner && ! $this->form->partner_has_lpa);
                break;
            default:
                break;
        }
    }

    /**
     * Whether to show the sub_questions for a given field.
     */
    private function showSubQuestions(FieldData $field): bool
    {
        $show = is_array($field->check) ? in_array($this->form->{$field->name}, $field->check) : $this->form->{$field->name} === $field->check;

        if (! $show) {
            foreach ($field->subQuestions as $subQuestion) {
                if (is_bool($this->form->{$subQuestion->name})) {
                    $this->form->{$subQuestion->name} = false;
                } else {
                    $this->form->{$subQuestion->name} = null;
                }
            }
        }

        return $show;
    }
};
?>

<x-flux::card class="col-start-1 row-start-4 row-span-4">
    @foreach ($fields as $index => $field)
        @if ($field->shouldDisplay)
            <livewire:pages.suitability_reports.partials.form_field :field="$field" wire:model.change="form"
                                                                    wire:key="form.{{ $field->name }}"/>
        @endif
        @if ($field->subQuestions && $this->showSubQuestions($field))
            <div class="ml-8">
                @foreach ($field->subQuestions as $subQuestion)
                    @if ($subQuestion->shouldDisplay)
                        <livewire:pages.suitability_reports.partials.form_field class="mt-2" :field="$subQuestion"
                                                                                wire:model.change="form"
                                                                                wire:key="form.{{ $subQuestion->name }}"/>
                    @endif
                @endforeach
            </div>
        @endif
        @if ($index !== count($fields) - 1 && $field->shouldDisplay)
            <hr class="my-6"/>
        @endif
    @endforeach
</x-flux::card>
