<?php

use App\Domains\SuitabilityReports\Forms\SuitabilityReportForm;
use App\Domains\SuitabilityReports\Models\SuitabilityReportCustomer;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;

new
#[Layout('layouts.app')]
class extends Component
{
    public SuitabilityReportCustomer $customer;

    public SuitabilityReportForm $form;

    public function mount(SuitabilityReportCustomer $customer): void
    {
        $this->customer = $customer;

        $this->form = new SuitabilityReportForm($this, 'suitability_report_form');

        $this->form->setFormFromReport($this->customer->report);
    }
}
?>

<x-slot name="header" wire:model="search">
    <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 flex gap-x-2 dark:text-gray-200 leading-tight">
            <flux:icon name="clipboard-document-list"/>
            {{ __('Internal Suitability Report - ') . ($this->customer->name) }}
        </h2>
        <flux:icon.eye/>
    </div>
</x-slot>
<div class="flex flex-col max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-14 gap-y-8">
    <!-- SuitabilityReport customer details  -->
    <livewire:pages.suitability_reports.partials.customer-details class="" wire:model.change="customer"
    />
    <div class="flex justify-between items-start gap-x-8">
        <!-- SuitabilityReport form  -->
        <livewire:pages.suitability_reports.components.form class="flex flex-col flex-2 gap-10 justify-between"
                                                            :form="$form"/>
        <div class="flex flex-col flex-1 gap-y-8 ">
            <!-- SuitabilityReport recommendations  -->
            <livewire:pages.suitability_reports.partials.recommendations :form="$form"/>
            <!-- SuitabilityReport notes  -->
            <flux:field>
                <flux:heading size="xl" class="mb-4">
                    {{ __('Notes') }}
                </flux:heading>
                <flux:textarea placeholder="Add notes here..." class="col-start-2 text-sm text-gray-500 !rounded-2xl"
                               resize="none"
                               rows="10" wire:model.live.debounce="form.notes"
                />
            </flux:field>
        </div>

    </div>

</div>

