<?php

use App\Domains\SuitabilityReports\Enums\EstateValue;
use App\Domains\SuitabilityReports\Forms\SuitabilityReportForm;
use App\Domains\SuitabilityReports\Models\SuitabilityReportCustomer;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;
use Livewire\Volt\Component;

new #[Layout('layouts.app')] class extends Component
{
    /**
     * The customer model reference.
     */
    public SuitabilityReportCustomer $customer;

    public SuitabilityReportForm $form;

    public string $customerName = '';

    public function mount(SuitabilityReportCustomer $customer): void
    {
        $this->customer = $customer;

        $this->form = new SuitabilityReportForm($this, 'suitability_report_form');

        $this->form->setFormFromReport($this->customer->report);

        $this->customerName = $this->customer->name;
    }

    #[On('has_partner_updated')]
    #[On('has_lpa_updated')]
    public function products(): string
    {
        return ($this->form->lpa ? 'Will ' : 'Will + LPA ') . ($this->form->has_partner ? '(couple)' : '(single)');
    }

    #[On('estate_value_updated')]
    public function iht(): string
    {
        return in_array($this->form->value_of_estate, [EstateValue::BETWEEN_1_AND_2_MILLION->name, EstateValue::OVER_2MILLION->name]) ? 'IHT Recommended' : 'IHT Not recommended';
    }

    #[On('has_children_updated')]
    public function trusts(): string
    {
        $recommended = $this->form->children || $this->form->step_children || $this->form->children_with_disability_or_care_needs;
        $notRecommended = ! $this->form->children && ! $this->form->step_children && ! $this->form->children_with_disability_or_care_needs;

        if ($recommended) {
            return 'Recommended';
        }

        if ($notRecommended) {
            return 'Not Recommended';
        }

        return '';
    }

    #[On('customer_name_updated')]
    public function pageTitle(): string
    {
        return __('Internal Suitability Reports') . ($this->customerName ? " - {$this->customerName}" : null);
    }

    public function updated(string $property): void
    {
        if ($property === 'customerName') {
            $this->customer->update([
                'name' => $this->customerName,
            ]);
            $this->dispatch('customer_name_updated');
        }

        $this->form->update($this->customer->report);
    }
};
?>

<x-slot name="header" wire:model="search">
    <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 flex gap-x-2 dark:text-gray-200 leading-tight">
            <flux:icon name="clipboard-document-list" />
            {{ $this->pageTitle() }}
        </h2>
    </div>
</x-slot>
<div class="grid gap-4 p-6 w-full max-w-6xl mx-auto">
    {{-- SuitabilityReportCustomer form  --}}
    <div class="col-span-2 flex items-center justify-between">
        <flux:heading size="lg" class=" ml-2 w-full">
            {{ __('Customer Details') }}
        </flux:heading>
        {{-- <flux:button disabled>Hubspot</flux:button> --}}
    </div>
    <x-flux::card class="col-span-2 flex justify-between gap-4">
        <flux:input label="Customer" wire:model.live.debounce="customerName" placeholder="Enter the customer's name" />
        <div class="flex flex-col items-end gap-2">
            <flux:label for="estate_planner_id" class="!m-0">
                {{ __('Estate Planner') }}
            </flux:label>
            <flux:description>
                {{ $customer->estatePlanner->name ?? __('No estate planner') }}
            </flux:description>
        </div>
    </x-flux::card>
    {{-- SuitabilityReport form --}}
    <flux:heading size="lg" class="ml-2">
        {{ __('Suitability') }}
    </flux:heading>
    <livewire:pages.suitability_reports.partials.report_form wire:model.change="form" :customer="$customer" />
    {{-- SuitabilityReportRecommendation results --}}
    <flux:heading size="lg" class="col-start-2 row-start-3 ml-2">
        {{ __('Recommendations') }}
    </flux:heading>
    <x-flux::card class="col-start-2 flex flex-col gap-4 h-fit">
        <flux:heading>{{ __('Products') }}</flux:heading>
        <flux:text>{{ $this->products() }}</flux:text>
        <flux:heading>{{ __('Trusts') }}</flux:heading>
        <flux:text>{{ $this->trusts() }}</flux:text>
        <flux:heading>{{ __('IHT') }}</flux:heading>
        <flux:text>{{ $this->iht() }}</flux:text>
    </x-flux::card>
    <flux:heading size="lg" class="col-start-2 row-start-5 ml-2">
        {{ __('Notes') }}
    </flux:heading>
    <flux:textarea placeholder="Add notes here..." class="col-start-2 text-sm text-gray-500 !rounded-2xl" resize="none"
        rows="10" wire:model.live.debounce="form.notes" />
</div>
