<?php

use App\Domains\Radar\Services\Ocr\OcrService;
use App\Domains\SuitabilityReports\Http\StartSuitabilityReportFromHubspotController;
use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;

Volt::route('/', 'pages.welcome')->name('welcome');

Route::middleware(['auth'])->group(function (): void {
    Route::group(['prefix' => 'radar'], function (): void {
        Volt::route('/import', 'pages.radar.import')->name('radar.import');
        Volt::route('/', 'pages.radar.index')->name('radar.index');
        Volt::route('/{id}', 'pages.radar.show')->name('radar.show');
    });

    Route::group(['prefix' => 'deals'], function (): void {
        Volt::route('/', 'pages.deals.index')->name('deals.index');
        Volt::route('/lead_sources', 'pages.deals.lead_sources.index')->name('deals.lead_sources.index');
    });

    Route::group(['prefix' => 'internal-suitability-report'], function (): void {
        Volt::route('/', 'pages.suitability_reports.index')->name('suitability.index');
        Volt::route('/{customer}', 'pages.suitability_reports.show-v2')->name('suitability.show');
        Route::get('hubspot/{hubspotId}', StartSuitabilityReportFromHubspotController::class)->whereAlphaNumeric('hubspotId');
    });

    // Documentation routes - protected by authentication
    Route::get('docs/{path?}', function ($path = '') {
        // Clean and secure the path
        $path = ltrim($path, '/');
        $path = str_replace(['../', './'], '', $path);

        // Default to index.html if no path or if path is a directory
        if (empty($path) || ! str_contains($path, '.')) {
            $path = rtrim($path, '/') . '/index.html';
            $path = ltrim($path, '/');
        }

        $filePath = public_path('docs/' . $path);

        // Check if file exists
        if (! file_exists($filePath) || ! is_file($filePath)) {
            abort(404);
        }

        return response()->file($filePath);
    })->where('path', '.*')->name('docs');
});

Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::view('profile', 'profile')
    ->middleware(['auth'])
    ->name('profile');

Route::get('test', function () {

    $ocr = app(OcrService::class);

    return $ocr->extract('will_pngs/SSB100080.png');

});

require __DIR__ . '/auth.php';
