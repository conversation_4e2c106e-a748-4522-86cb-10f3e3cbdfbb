<?php

namespace Tests\Unit\Domains\Leads;

use App\Domains\Leads\Models\Deal;
use Carbon\Carbon;
use Tests\TestCase;

class BreachCountQueryTest extends TestCase
{
    /** @test */
    public function breach_count_query_logic_is_correct()
    {
        // This test verifies the SQL query logic for breach count
        // We test the raw SQL logic that should be used in the dashboard

        Carbon::setTestNow(Carbon::create(2023, 12, 18, 12, 0, 0)); // Monday at 12:00 PM

        // Test the correct query logic using PostgreSQL JSONB syntax
        $currentTime = Carbon::now('Europe/London');

        // Scenario 1: Deal with first call breached (call_count = 0)
        $deal1CallSlas = [
            Carbon::now()->subHour()->toISOString(), // First call SLA was 1 hour ago (BREACHED)
            Carbon::now()->addHour()->toISOString(), // Second call SLA is in 1 hour
        ];
        $deal1CallCount = 0; // Currently on first call

        // The correct query should check: call_slas[0] < current_time
        // In PostgreSQL: (call_slas::jsonb->0)::text::timestamp < current_time
        $deal1ShouldBeBreach = Carbon::parse($deal1CallSlas[$deal1CallCount])->lt($currentTime);
        $this->assertTrue($deal1ShouldBeBreach, 'Deal 1 should be breached (first call SLA is past)');

        // Scenario 2: Deal with second call breached (call_count = 1)
        $deal2CallSlas = [
            Carbon::now()->subHours(2)->toISOString(), // First call SLA was 2 hours ago
            Carbon::now()->subMinutes(30)->toISOString(), // Second call SLA was 30 minutes ago (BREACHED)
        ];
        $deal2CallCount = 1; // Currently on second call

        // The correct query should check: call_slas[1] < current_time
        // In PostgreSQL: (call_slas::jsonb->1)::text::timestamp < current_time
        $deal2ShouldBeBreach = Carbon::parse($deal2CallSlas[$deal2CallCount])->lt($currentTime);
        $this->assertTrue($deal2ShouldBeBreach, 'Deal 2 should be breached (second call SLA is past)');

        // Scenario 3: Deal with first call past but currently on second call (not breached)
        $deal3CallSlas = [
            Carbon::now()->subHours(3)->toISOString(), // First call SLA was 3 hours ago (PAST)
            Carbon::now()->addHour()->toISOString(),   // Second call SLA is in 1 hour (NOT BREACHED)
        ];
        $deal3CallCount = 1; // Currently on second call

        // The correct query should check: call_slas[1] < current_time
        // This should be FALSE because we only care about the current call's SLA
        $deal3ShouldBeBreach = Carbon::parse($deal3CallSlas[$deal3CallCount])->lt($currentTime);
        $this->assertFalse($deal3ShouldBeBreach, 'Deal 3 should NOT be breached (current call SLA is future)');

        // Scenario 4: Deal with no breaches
        $deal4CallSlas = [
            Carbon::now()->addHour()->toISOString(),   // First call SLA is in 1 hour
            Carbon::now()->addHours(2)->toISOString(), // Second call SLA is in 2 hours
        ];
        $deal4CallCount = 0; // Currently on first call

        $deal4ShouldBeBreach = Carbon::parse($deal4CallSlas[$deal4CallCount])->lt($currentTime);
        $this->assertFalse($deal4ShouldBeBreach, 'Deal 4 should NOT be breached (first call SLA is future)');

        // This demonstrates that the correct PostgreSQL query should be:
        // (call_slas::jsonb->call_count)::text::timestamp < ?
        // NOT:
        // EXISTS (SELECT 1 FROM jsonb_array_elements_text(call_slas::jsonb) AS sla(ts) WHERE (sla.ts)::timestamp < ?)
    }

    /** @test */
    public function demonstrates_old_vs_new_query_logic()
    {
        Carbon::setTestNow(Carbon::create(2023, 12, 18, 12, 0, 0));
        $currentTime = Carbon::now('Europe/London');

        // Example deal: First call was missed, but currently on second call which is not breached
        $callSlas = [
            Carbon::now()->subHours(3)->toISOString(), // First call SLA was 3 hours ago (PAST)
            Carbon::now()->addHour()->toISOString(),   // Second call SLA is in 1 hour (FUTURE)
        ];
        $callCount = 1; // Currently on second call

        // OLD QUERY LOGIC (INCORRECT):
        // EXISTS (SELECT 1 FROM jsonb_array_elements_text(call_slas::jsonb) AS sla(ts) WHERE (sla.ts)::timestamp < ?)
        // This checks if ANY SLA is breached
        $oldLogicWouldCount = false;
        foreach ($callSlas as $sla) {
            if (Carbon::parse($sla)->lt($currentTime)) {
                $oldLogicWouldCount = true;
                break;
            }
        }
        $this->assertTrue($oldLogicWouldCount, 'Old logic would incorrectly count this as breached');

        // NEW QUERY LOGIC (CORRECT):
        // (call_slas::jsonb->call_count)::text::timestamp < ?
        // This checks only the CURRENT call's SLA
        $newLogicWouldCount = Carbon::parse($callSlas[$callCount])->lt($currentTime);
        $this->assertFalse($newLogicWouldCount, 'New logic correctly does NOT count this as breached');

        // Verify this matches the Deal model's logic
        $deal = new Deal([
            'call_count' => $callCount,
            'call_slas' => $callSlas,
        ]);
        $this->assertFalse($deal->isSlaBreached(), 'Deal model correctly identifies this as NOT breached');
        $this->assertEquals($newLogicWouldCount, $deal->isSlaBreached(), 'New query logic matches Deal model logic');
    }
}
