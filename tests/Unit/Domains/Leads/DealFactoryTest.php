<?php

namespace Tests\Unit\Domains\Leads;

use App\Domains\Leads\Models\Deal;
use Carbon\Carbon;
use Tests\TestCase;

class DealFactoryTest extends TestCase
{
    /** @test */
    public function deal_factory_creates_realistic_test_data()
    {
        // Create a deal using the factory with explicit lead source
        $deal = Deal::factory()->make([
            'lead_source' => 'Test Source',
            'call_slas' => [
                Carbon::now()->addHour()->toISOString(),
                Carbon::now()->addHours(2)->toISOString(),
                Carbon::now()->addHours(3)->toISOString(),
            ],
        ]);

        // Verify the deal has realistic data
        $this->assertNotEmpty($deal->deal_name);
        $this->assertNotEmpty($deal->lead_source);
        $this->assertIsArray($deal->call_slas);
        $this->assertGreaterThanOrEqual(0, $deal->call_count);
        $this->assertGreaterThan(0, $deal->ranking);
        $this->assertLessThanOrEqual(100, $deal->ranking);

        // Verify SLA timestamps are valid ISO strings
        foreach ($deal->call_slas as $sla) {
            $this->assertNotFalse(Carbon::parse($sla), 'SLA should be a valid timestamp');
        }
    }

    /** @test */
    public function deal_factory_breached_state_creates_breached_deals()
    {
        Carbon::setTestNow(Carbon::create(2023, 12, 18, 12, 0, 0));

        $deal = Deal::factory()->breached()->make();

        // Verify the deal is definitely breached
        $this->assertTrue($deal->isSlaBreached(), 'Breached factory state should create breached deals');
        $this->assertGreaterThanOrEqual(80, $deal->ranking, 'Breached deals should have high priority');

        // Verify current call SLA is in the past
        $currentSla = Carbon::parse($deal->call_slas[$deal->call_count]);
        $this->assertTrue($currentSla->isPast(), 'Current call SLA should be in the past');
    }

    /** @test */
    public function deal_factory_within_sla_state_creates_non_breached_deals()
    {
        Carbon::setTestNow(Carbon::create(2023, 12, 18, 12, 0, 0));

        $deal = Deal::factory()->withinSla()->make();

        // Verify the deal is definitely not breached
        $this->assertFalse($deal->isSlaBreached(), 'WithinSla factory state should create non-breached deals');
        $this->assertLessThanOrEqual(50, $deal->ranking, 'Non-urgent deals should have lower priority');

        // Verify current call SLA is in the future
        $currentSla = Carbon::parse($deal->call_slas[$deal->call_count]);
        $this->assertTrue($currentSla->isFuture(), 'Current call SLA should be in the future');
    }

    /** @test */
    public function factory_states_work_correctly()
    {
        Carbon::setTestNow(Carbon::create(2023, 12, 18, 12, 0, 0));

        // Test that we can create deals with specific states
        $breachedDeal = Deal::factory()->breached()->make();
        $withinSlaDeal = Deal::factory()->withinSla()->make();

        // Basic validation that the states produce different results
        $this->assertTrue($breachedDeal->isSlaBreached(), 'Breached factory should create breached deals');
        $this->assertFalse($withinSlaDeal->isSlaBreached(), 'WithinSla factory should create non-breached deals');

        // Verify priority differences
        $this->assertGreaterThanOrEqual(80, $breachedDeal->ranking, 'Breached deals should have high priority');
        $this->assertLessThanOrEqual(50, $withinSlaDeal->ranking, 'Non-urgent deals should have lower priority');
    }
}
