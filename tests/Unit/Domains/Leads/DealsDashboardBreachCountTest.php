<?php

namespace Tests\Unit\Domains\Leads;

use App\Domains\Leads\Models\Deal;
use Carbon\Carbon;
use Tests\TestCase;

class DealsDashboardBreachCountTest extends TestCase
{
    /** @test */
    public function individual_deal_breach_detection_works_correctly()
    {
        // Set a fixed time for testing
        Carbon::setTestNow(Carbon::create(2023, 12, 18, 12, 0, 0)); // Monday at 12:00 PM

        // Create a deal where first call SLA is breached but second call SLA is not
        $dealWithFirstCallBreached = new Deal([
            'call_count' => 0, // Currently on first call
            'call_slas' => [
                Carbon::now()->subHour()->toISOString(), // First call SLA was 1 hour ago (BREACHED)
                Carbon::now()->addHour()->toISOString(), // Second call SLA is in 1 hour (NOT BREACHED)
            ],
        ]);

        // Create a deal where first call SLA is not breached but second call SLA is breached
        $dealWithSecondCallBreached = new Deal([
            'call_count' => 1, // Currently on second call
            'call_slas' => [
                Carbon::now()->subHours(2)->toISOString(), // First call SLA was 2 hours ago
                Carbon::now()->subMinutes(30)->toISOString(), // Second call SLA was 30 minutes ago (BREACHED)
            ],
        ]);

        // Create a deal where no SLAs are breached
        $dealWithNoBreaches = new Deal([
            'call_count' => 0, // Currently on first call
            'call_slas' => [
                Carbon::now()->addHour()->toISOString(), // First call SLA is in 1 hour (NOT BREACHED)
                Carbon::now()->addHours(2)->toISOString(), // Second call SLA is in 2 hours (NOT BREACHED)
            ],
        ]);

        // Test individual deal breach detection (this should work correctly)
        $this->assertTrue($dealWithFirstCallBreached->isSlaBreached(), 'Deal with first call breached should be breached');
        $this->assertTrue($dealWithSecondCallBreached->isSlaBreached(), 'Deal with second call breached should be breached');
        $this->assertFalse($dealWithNoBreaches->isSlaBreached(), 'Deal with no breaches should not be breached');
    }

    /** @test */
    public function demonstrates_the_dashboard_breach_count_issue()
    {
        // This test demonstrates the conceptual issue with the current breach count query

        Carbon::setTestNow(Carbon::create(2023, 12, 18, 12, 0, 0));

        // Scenario: Deal with first call breached, but currently on second call (not breached)
        $deal = new Deal([
            'call_count' => 1, // Currently on SECOND call
            'call_slas' => [
                Carbon::now()->subHours(3)->toISOString(), // First call SLA was 3 hours ago (BREACHED)
                Carbon::now()->addHour()->toISOString(),   // Second call SLA is in 1 hour (NOT BREACHED)
            ],
        ]);

        // The individual deal model correctly identifies this as NOT breached
        // because it only checks the CURRENT call's SLA (second call in this case)
        $this->assertFalse($deal->isSlaBreached(), 'Deal should NOT be breached because current call SLA is not breached');

        // However, the current dashboard query would count this as breached because:
        // EXISTS (SELECT 1 FROM jsonb_array_elements_text(call_slas::jsonb) AS sla(ts) WHERE (sla.ts)::timestamp < now())
        // This checks if ANY SLA timestamp is breached, not just the current one

        // The correct query should be:
        // (call_slas::jsonb->call_count)::text::timestamp < now()
        // This would only check the SLA for the current call attempt

        $this->assertTrue(true, 'This test demonstrates the conceptual issue - see comments above');
    }
}
